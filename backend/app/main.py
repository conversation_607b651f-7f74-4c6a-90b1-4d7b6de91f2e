from fastapi import FastAP<PERSON>
from fastapi.middleware.cors import CORSMiddleware
from app.api import auth, protected, cases
from app.core.database import engine, Base

# Import all models to ensure they are registered with Base
from app.models.user import User
from app.models.case import Case
from app.models.collection import Collection
from app.models.case_file import CaseFile
from app.models.namespace import Namespace

# Create all database tables
print("Creating database tables...")
Base.metadata.create_all(bind=engine)
print("Database tables created successfully!")

app = FastAPI()

app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # Adjust for production
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

app.include_router(auth.router, prefix="/auth", tags=["auth"])
app.include_router(protected.router, tags=["protected"])
app.include_router(cases.router, prefix="/api", tags=["cases"])
