from sqlalchemy.orm import Session
from typing import List, Optional
import uuid
from datetime import datetime

from app.models.case import Case, CaseCreate, CaseUpdate, CaseResponse, CaseWithProcessingStatus
from app.models.collection import Collection, TreeNode
from app.models.case_file import CaseFile
from app.models.user import User

class CaseService:
    @staticmethod
    def create_case(db: Session, case_data: CaseCreate, user_id: str) -> CaseResponse:
        """Create a new case"""
        # Validate collection exists if provided
        if case_data.collection_id:
            collection = db.query(Collection).filter(
                Collection.id == case_data.collection_id,
                Collection.user_id == user_id
            ).first()
            if not collection:
                raise ValueError("Collection not found")
        
        # Check if internal case number is unique
        if case_data.internal_case_number:
            existing_case = db.query(Case).filter(
                Case.internal_case_number == case_data.internal_case_number
            ).first()
            if existing_case:
                raise ValueError("Internal case number already exists")
        
        # Create case
        case_id = str(uuid.uuid4())
        db_case = Case(
            id=case_id,
            user_id=user_id,
            **case_data.dict()
        )
        
        db.add(db_case)
        db.commit()
        db.refresh(db_case)
        
        return CaseResponse(
            **db_case.__dict__,
            files_count=0
        )
    
    @staticmethod
    def get_cases(
        db: Session, 
        user_id: str, 
        skip: int = 0, 
        limit: int = 100,
        collection_id: Optional[str] = None
    ) -> List[CaseResponse]:
        """Get all cases for a user"""
        query = db.query(Case).filter(Case.user_id == user_id)
        
        if collection_id:
            query = query.filter(Case.collection_id == collection_id)
        
        cases = query.offset(skip).limit(limit).all()
        
        result = []
        for case in cases:
            files_count = db.query(CaseFile).filter(CaseFile.case_id == case.id).count()
            result.append(CaseResponse(
                **case.__dict__,
                files_count=files_count
            ))
        
        return result

    @staticmethod
    def get_all_cases_with_status(
        db: Session,
        user_id: str,
        skip: int = 0,
        limit: int = 100
    ) -> List[CaseWithProcessingStatus]:
        """Get all cases for a user with document processing status"""
        cases = db.query(Case).filter(Case.user_id == user_id).offset(skip).limit(limit).all()

        result = []
        for case in cases:
            # Get file statistics
            files = db.query(CaseFile).filter(CaseFile.case_id == case.id).all()
            total_files = len(files)

            # Count files by status
            pending_files = len([f for f in files if f.upload_status == "pending"])
            uploading_files = len([f for f in files if f.upload_status == "uploading"])
            completed_files = len([f for f in files if f.upload_status == "completed"])
            error_files = len([f for f in files if f.upload_status == "error"])

            # Calculate processing status and progress
            if total_files == 0:
                processing_status = "not_started"
                processing_progress = 0
            elif error_files > 0:
                processing_status = "error"
                processing_progress = int((completed_files / total_files) * 100)
            elif pending_files > 0 or uploading_files > 0:
                processing_status = "in_progress"
                processing_progress = int((completed_files / total_files) * 100)
            else:
                processing_status = "completed"
                processing_progress = 100

            # Get collection name if case is in a collection
            collection_name = None
            if case.collection_id:
                collection = db.query(Collection).filter(Collection.id == case.collection_id).first()
                if collection:
                    collection_name = collection.name

            result.append(CaseWithProcessingStatus(
                **case.__dict__,
                files_count=total_files,
                processing_status=processing_status,
                processing_progress=processing_progress,
                total_files=total_files,
                processed_files=completed_files,
                pending_files=pending_files + uploading_files,
                error_files=error_files,
                collection_name=collection_name
            ))

        return result

    @staticmethod
    def get_case(db: Session, case_id: str, user_id: str) -> CaseResponse:
        """Get a specific case by ID"""
        case = db.query(Case).filter(
            Case.id == case_id,
            Case.user_id == user_id
        ).first()
        
        if not case:
            raise ValueError("Case not found")
        
        files_count = db.query(CaseFile).filter(CaseFile.case_id == case_id).count()
        
        return CaseResponse(
            **case.__dict__,
            files_count=files_count
        )
    
    @staticmethod
    def update_case(
        db: Session, 
        case_id: str, 
        case_data: CaseUpdate, 
        user_id: str
    ) -> CaseResponse:
        """Update a case"""
        case = db.query(Case).filter(
            Case.id == case_id,
            Case.user_id == user_id
        ).first()
        
        if not case:
            raise ValueError("Case not found")
        
        # Validate collection exists if provided
        if case_data.collection_id:
            collection = db.query(Collection).filter(
                Collection.id == case_data.collection_id,
                Collection.user_id == user_id
            ).first()
            if not collection:
                raise ValueError("Collection not found")
        
        # Check if internal case number is unique (if being updated)
        if case_data.internal_case_number and case_data.internal_case_number != case.internal_case_number:
            existing_case = db.query(Case).filter(
                Case.internal_case_number == case_data.internal_case_number,
                Case.id != case_id
            ).first()
            if existing_case:
                raise ValueError("Internal case number already exists")
        
        # Update case
        update_data = case_data.dict(exclude_unset=True)
        for field, value in update_data.items():
            setattr(case, field, value)
        
        case.updated_at = datetime.utcnow()
        db.commit()
        db.refresh(case)
        
        files_count = db.query(CaseFile).filter(CaseFile.case_id == case_id).count()
        
        return CaseResponse(
            **case.__dict__,
            files_count=files_count
        )
    
    @staticmethod
    def delete_case(db: Session, case_id: str, user_id: str) -> bool:
        """Delete a case and all its files"""
        case = db.query(Case).filter(
            Case.id == case_id,
            Case.user_id == user_id
        ).first()
        
        if not case:
            raise ValueError("Case not found")
        
        # Delete case (files will be deleted due to cascade)
        db.delete(case)
        db.commit()
        
        return True
    
    @staticmethod
    def move_case(
        db: Session,
        case_id: str,
        collection_id: Optional[str],
        user_id: str
    ) -> bool:
        """Move a case to a different collection"""
        print(f"DEBUG: Moving case {case_id} to collection {collection_id} for user {user_id}")

        case = db.query(Case).filter(
            Case.id == case_id,
            Case.user_id == user_id
        ).first()

        if not case:
            print(f"DEBUG: Case not found - case_id: {case_id}, user_id: {user_id}")
            raise ValueError("Case not found")

        print(f"DEBUG: Found case: {case.case_name}, current collection_id: {case.collection_id}")

        # Validate collection exists if provided
        if collection_id:
            collection = db.query(Collection).filter(
                Collection.id == collection_id,
                Collection.user_id == user_id
            ).first()
            if not collection:
                print(f"DEBUG: Collection not found - collection_id: {collection_id}, user_id: {user_id}")
                raise ValueError("Collection not found")
            print(f"DEBUG: Found target collection: {collection.name}")
        else:
            print("DEBUG: Moving case to root (no collection)")

        # Update case collection
        old_collection_id = case.collection_id
        case.collection_id = collection_id
        case.updated_at = datetime.utcnow()

        print(f"DEBUG: Updated case collection_id from {old_collection_id} to {collection_id}")

        try:
            db.commit()
            print("DEBUG: Database commit successful")
        except Exception as e:
            print(f"DEBUG: Database commit failed: {e}")
            db.rollback()
            raise

        # Verify the change was persisted
        db.refresh(case)
        print(f"DEBUG: After commit, case collection_id is: {case.collection_id}")

        return True