import os
import sys
import time
from fastapi import HTTPException
from typing import List, Dict, Union, Any
import json
import uuid
import copy

from app.models.namespace import Namespace  # Import the Namespace model
from app.core.config import settings
from sqlalchemy.orm import Session

# Optional imports for embedding functionality
try:
    from langchain_unstructured import UnstructuredLoader
    from langchain_community.document_loaders import PyPDFLoader, UnstructuredWordDocumentLoader
    from langchain_community.document_loaders.excel import UnstructuredExcelLoader
    from langchain_openai import OpenAIEmbeddings
    from pinecone import Pinecone
    from langchain_core.utils.iter import batch_iterate
    import nltk
    from dotenv import load_dotenv

    EMBEDDING_DEPENDENCIES_AVAILABLE = True
    print("✓ Embedding dependencies loaded successfully")
except ImportError as e:
    EMBEDDING_DEPENDENCIES_AVAILABLE = False
    print(f"⚠️  Embedding dependencies not available: {e}")
    print("To enable PDF embedding, install: pip install langchain-unstructured langchain-community langchain-openai pinecone langchain-core nltk python-dotenv")

# Initialize NLTK and environment variables only if dependencies are available
if EMBEDDING_DEPENDENCIES_AVAILABLE:
    # Download required NLTK data
    try:
        nltk.download('punkt', quiet=True)
        nltk.download('punkt_tab', quiet=True)
        nltk.download('averaged_perceptron_tagger_eng', quiet=True)
        nltk.download('averaged_perceptron_tagger', quiet=True)
    except Exception as e:
        print(f"Warning: Could not download NLTK data: {e}")

    # Load environment variables
    load_dotenv()

    # Get environment variables
    OPENAI_API_KEY = settings.OPENAI_API_KEY
    PINECONE_KEY = settings.PINECONE_API_KEY
    PINECONE_INDEX = settings.PINECONE_INDEX

    # Validate environment variables
    if not all([OPENAI_API_KEY, PINECONE_KEY, PINECONE_INDEX]):
        print("Warning: Missing required environment variables for embedding!")
        EMBEDDING_DEPENDENCIES_AVAILABLE = False


def clean_metadata_value(value: Any) -> Union[str, int, float, bool, List[str]]:
    """
    Clean metadata values to ensure they're Pinecone-compatible.
    Pinecone only accepts strings, numbers, booleans, or lists of strings.
    """
    if isinstance(value, (str, int, float, bool)):
        return value
    elif isinstance(value, list):
        return [str(item) for item in value]
    elif isinstance(value, dict):
        return json.dumps(value)
    else:
        return str(value)


def clean_metadata(metadata: Dict) -> Dict:
    """Clean metadata dictionary to ensure all values are Pinecone-compatible."""
    cleaned = {}
    for key, value in metadata.items():
        if key != 'coordinates':  # Skip coordinates field entirely
            cleaned[key] = clean_metadata_value(value)
    return cleaned


def string_size_in_kb(s: str) -> float:
    """Calculate the size of a string in kilobytes."""
    size_in_bytes = sys.getsizeof(s)
    return size_in_bytes / 1024


def split_text_by_kb(text: str, number: int) -> List[str]:
    """Split text into chunks based on the specified number of divisions."""
    chunks = []
    start = 0
    distance = int(len(text) / number) + 1

    while start < len(text):
        end = min(start + distance, len(text))
        chunk = text[start:end]
        chunks.append(chunk)
        start = end

    return chunks


def process_and_train(file_path: str, namespace: str = None):
    """
    Process and train documents using Pinecone and OpenAI embeddings.
    """
    # Check if embedding dependencies are available
    if not EMBEDDING_DEPENDENCIES_AVAILABLE:
        raise HTTPException(
            status_code=503,
            detail="Embedding functionality is not available. Please install required dependencies: pip install langchain-unstructured langchain-community langchain-openai pinecone langchain-core nltk python-dotenv"
        )

    try:
        if not os.path.exists(file_path):
            raise HTTPException(
                status_code=404, detail=f"File not found: {file_path}")
        print(
            f"Starting document processing and training for file: {file_path}")

        # Load the document
        try:
            if file_path.lower().endswith('.pdf'):
                print(file_path)
                loader = PyPDFLoader(file_path=file_path)

            elif file_path.lower().endswith('.docx'):
                print(file_path)
                loader = UnstructuredWordDocumentLoader(file_path=file_path)

            elif file_path.lower().endswith('.xlsx'):
                print(file_path)
                loader = UnstructuredExcelLoader(file_path=file_path)

            else:
                # Skip the file if it's not a PDF or DOCX
                print(f"Skipping file {file_path}, not a PDF or DOCX")
                return {"status": "skipped", "message": "File type not supported"}

            docs = loader.load()
        except Exception as e:
            print(f"Failed to load {file_path}. Error: {e}")
            raise HTTPException(status_code=500, detail=f"Failed to load document: {str(e)}")

        # Initialize OpenAI embeddings
        embeddings_model = OpenAIEmbeddings(openai_api_key=OPENAI_API_KEY)

        # Initialize Pinecone
        pc = Pinecone(api_key=PINECONE_KEY)
        existing_indexes = [index_info["name"]
                            for index_info in pc.list_indexes()]
        print(f"Existing Pinecone indexes: {existing_indexes}")

        # Create index if it doesn't exist
        if PINECONE_INDEX not in existing_indexes:
            print(f"Creating new index: {PINECONE_INDEX}")
            pc.create_index(
                name=PINECONE_INDEX,
                dimension=1536,
                metric="cosine"
            )

        index = pc.Index(PINECONE_INDEX)

        # Process documents
        texts = []
        metadatas = []

        # Split documents into appropriate chunks
        for doc in docs:
            encode_size = string_size_in_kb(doc.page_content)
            if encode_size > 40:
                divide_number = int(encode_size / 40) + 1
                print(divide_number)
                split_encoded_texts = split_text_by_kb(doc.page_content, divide_number)
                for chuck in split_encoded_texts:
                    texts.append(chuck)
                    metadatas.append(doc.metadata)
            else:
                texts.append(doc.page_content)
                metadatas.append(doc.metadata)

        print(f"Processing {len(texts)} text chunks")

        if not texts:
            raise HTTPException(
                status_code=500,
                detail="No text content extracted from document"
            )

        # Prepare data for embedding
        embedding_chunk_size = 1000
        filename_with_extension = os.path.basename(file_path)
        if namespace is None:
            namespace = os.path.splitext(filename_with_extension)[0]
        print("---------=>", namespace)

        texts = list(texts)
        ids = [str(uuid.uuid4()) for _ in texts]
        metadatas = metadatas or [{} for _ in texts]
        metadatas = [copy.deepcopy(metadata) for metadata in metadatas]
        for metadata, text in zip(metadatas, texts):
            metadata["text"] = text

        for i in range(0, len(texts), embedding_chunk_size):
            chunk_texts = texts[i : i + embedding_chunk_size]
            chunk_ids = ids[i : i + embedding_chunk_size]
            chunk_metadatas = metadatas[i : i + embedding_chunk_size]
            embeddings = embeddings_model.embed_documents(chunk_texts)
            vector_tuples = zip(chunk_ids, embeddings, chunk_metadatas)

            # Runs the pinecone upsert asynchronously.
            async_res = [
                index.upsert(
                    vectors=batch_vector_tuples,
                    namespace=namespace,
                    async_req=True,
                )
                for batch_vector_tuples in batch_iterate(32, vector_tuples)
            ]
            [res.get() for res in async_res]

        # Insert namespace into the database
        new_namespace = Namespace(
            id=str(uuid.uuid4()),
            namespacename=namespace,
            documenttitle=os.path.basename(file_path)
        )

        while True:
            index_stats = index.describe_index_stats()
            if namespace in index_stats["namespaces"]:
                break
            time.sleep(1)
        try:
            os.remove(file_path)
        except Exception as e:
            print(f"Error deleting file {file_path}: {e}")

        print("Training completed successfully")

        return {
            "status": "success",
            "message": "Data training completed successfully",
            "details": {
                "file_name": os.path.basename(file_path),
                "chunks_processed": len(texts),
                "namespace": namespace,
            }
        }
    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=f"Error during document processing: {str(e)}"
        )
