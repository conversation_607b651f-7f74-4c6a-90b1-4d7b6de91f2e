import os
import sys
import time
import logging
from fastapi import HTTPException
from typing import List, Dict, Union, Any
import json
import uuid
import copy

from app.models.namespace import Namespace  # Import the Namespace model
from app.core.config import settings
from sqlalchemy.orm import Session

# Configure logging
logger = logging.getLogger(__name__)

# Optional imports for embedding functionality
try:
    from langchain_unstructured import UnstructuredLoader
    from langchain_community.document_loaders import PyPDFLoader, UnstructuredWordDocumentLoader
    from langchain_community.document_loaders.excel import UnstructuredExcelLoader
    from langchain_openai import OpenAIEmbeddings
    from pinecone import Pinecone
    from langchain_core.utils.iter import batch_iterate
    import nltk
    from dotenv import load_dotenv

    EMBEDDING_DEPENDENCIES_AVAILABLE = True
    logger.info("✓ Embedding dependencies loaded successfully")
except ImportError as e:
    EMBEDDING_DEPENDENCIES_AVAILABLE = False
    logger.warning(f"⚠️  Embedding dependencies not available: {e}")
    logger.info("To enable PDF embedding, install: pip install langchain-unstructured langchain-community langchain-openai pinecone langchain-core nltk python-dotenv")

# Initialize NLTK and environment variables only if dependencies are available
if EMBEDDING_DEPENDENCIES_AVAILABLE:
    # Download required NLTK data
    try:
        nltk.download('punkt', quiet=True)
        nltk.download('punkt_tab', quiet=True)
        nltk.download('averaged_perceptron_tagger_eng', quiet=True)
        nltk.download('averaged_perceptron_tagger', quiet=True)
    except Exception as e:
        logger.warning(f"Could not download NLTK data: {e}")

    # Load environment variables
    load_dotenv()

    # Get environment variables
    OPENAI_API_KEY = settings.OPENAI_API_KEY
    PINECONE_KEY = settings.PINECONE_API_KEY
    PINECONE_INDEX = settings.PINECONE_INDEX

    # Validate environment variables
    if not all([OPENAI_API_KEY, PINECONE_KEY, PINECONE_INDEX]):
        logger.warning("Missing required environment variables for embedding!")
        EMBEDDING_DEPENDENCIES_AVAILABLE = False


def clean_metadata_value(value: Any) -> Union[str, int, float, bool, List[str]]:
    """
    Clean metadata values to ensure they're Pinecone-compatible.
    Pinecone only accepts strings, numbers, booleans, or lists of strings.
    """
    if isinstance(value, (str, int, float, bool)):
        return value
    elif isinstance(value, list):
        return [str(item) for item in value]
    elif isinstance(value, dict):
        return json.dumps(value)
    else:
        return str(value)


def clean_metadata(metadata: Dict) -> Dict:
    """Clean metadata dictionary to ensure all values are Pinecone-compatible."""
    cleaned = {}
    for key, value in metadata.items():
        if key != 'coordinates':  # Skip coordinates field entirely
            cleaned[key] = clean_metadata_value(value)
    return cleaned


def string_size_in_kb(s: str) -> float:
    """Calculate the size of a string in kilobytes."""
    size_in_bytes = sys.getsizeof(s)
    return size_in_bytes / 1024


def split_text_by_kb(text: str, number: int) -> List[str]:
    """Split text into chunks based on the specified number of divisions."""
    chunks = []
    start = 0
    distance = int(len(text) / number) + 1

    while start < len(text):
        end = min(start + distance, len(text))
        chunk = text[start:end]
        chunks.append(chunk)
        start = end

    return chunks


def process_and_train(file_path: str, namespace: str = None):
    """
    Process and train documents using Pinecone and OpenAI embeddings.
    """
    logger.info(f"Starting process_and_train for file: {file_path}, namespace: {namespace}")

    # Check if embedding dependencies are available
    if not EMBEDDING_DEPENDENCIES_AVAILABLE:
        logger.error("Embedding dependencies not available")
        raise HTTPException(
            status_code=503,
            detail="Embedding functionality is not available. Please install required dependencies: pip install langchain-unstructured langchain-community langchain-openai pinecone langchain-core nltk python-dotenv"
        )

    try:
        if not os.path.exists(file_path):
            logger.error(f"File not found: {file_path}")
            raise HTTPException(
                status_code=404, detail=f"File not found: {file_path}")

        logger.info(f"File exists, starting document processing for: {file_path}")

        # Load the document
        try:
            if file_path.lower().endswith('.pdf'):
                logger.info(f"Loading PDF file: {file_path}")
                loader = PyPDFLoader(file_path=file_path)

            elif file_path.lower().endswith('.docx'):
                logger.info(f"Loading DOCX file: {file_path}")
                loader = UnstructuredWordDocumentLoader(file_path=file_path)

            elif file_path.lower().endswith('.xlsx'):
                logger.info(f"Loading XLSX file: {file_path}")
                loader = UnstructuredExcelLoader(file_path=file_path)

            else:
                # Skip the file if it's not a PDF or DOCX
                logger.warning(f"Unsupported file type for {file_path}, skipping")
                return {"status": "skipped", "message": "File type not supported"}

            logger.info(f"Loading document content from {file_path}")
            docs = loader.load()
            logger.info(f"Successfully loaded {len(docs)} document pages/sections")
        except Exception as e:
            logger.error(f"Failed to load {file_path}. Error: {e}")
            raise HTTPException(status_code=500, detail=f"Failed to load document: {str(e)}")

        # Initialize OpenAI embeddings
        logger.info("Initializing OpenAI embeddings model")
        embeddings_model = OpenAIEmbeddings(openai_api_key=OPENAI_API_KEY)

        # Initialize Pinecone
        logger.info("Initializing Pinecone connection")
        pc = Pinecone(api_key=PINECONE_KEY)
        existing_indexes = [index_info["name"]
                            for index_info in pc.list_indexes()]
        logger.info(f"Existing Pinecone indexes: {existing_indexes}")

        # Create index if it doesn't exist
        if PINECONE_INDEX not in existing_indexes:
            logger.info(f"Creating new Pinecone index: {PINECONE_INDEX}")
            pc.create_index(
                name=PINECONE_INDEX,
                dimension=1536,
                metric="cosine"
            )
        else:
            logger.info(f"Using existing Pinecone index: {PINECONE_INDEX}")

        index = pc.Index(PINECONE_INDEX)

        # Process documents
        logger.info("Starting document processing and chunking")
        texts = []
        metadatas = []

        # Split documents into appropriate chunks
        for i, doc in enumerate(docs):
            encode_size = string_size_in_kb(doc.page_content)
            logger.debug(f"Document {i+1}: size = {encode_size:.2f} KB")

            if encode_size > 40:
                divide_number = int(encode_size / 40) + 1
                logger.info(f"Document {i+1} is large ({encode_size:.2f} KB), splitting into {divide_number} chunks")
                split_encoded_texts = split_text_by_kb(doc.page_content, divide_number)
                for j, chunk in enumerate(split_encoded_texts):
                    texts.append(chunk)
                    metadatas.append(doc.metadata)
                    logger.debug(f"Created chunk {j+1}/{len(split_encoded_texts)} for document {i+1}")
            else:
                logger.debug(f"Document {i+1} is small enough ({encode_size:.2f} KB), keeping as single chunk")
                texts.append(doc.page_content)
                metadatas.append(doc.metadata)

        logger.info(f"Total text chunks to process: {len(texts)}")

        if not texts:
            logger.error("No text content extracted from document")
            raise HTTPException(
                status_code=500,
                detail="No text content extracted from document"
            )

        # Prepare data for embedding
        embedding_chunk_size = 1000
        filename_with_extension = os.path.basename(file_path)
        if namespace is None:
            namespace = os.path.splitext(filename_with_extension)[0]
        logger.info(f"Using namespace: {namespace}")

        texts = list(texts)
        ids = [str(uuid.uuid4()) for _ in texts]
        metadatas = metadatas or [{} for _ in texts]
        metadatas = [copy.deepcopy(metadata) for metadata in metadatas]

        logger.info("Adding text content to metadata")
        for metadata, text in zip(metadatas, texts):
            metadata["text"] = text

        logger.info(f"Starting embedding generation and Pinecone upsert in chunks of {embedding_chunk_size}")
        total_chunks = (len(texts) + embedding_chunk_size - 1) // embedding_chunk_size

        for i in range(0, len(texts), embedding_chunk_size):
            chunk_num = (i // embedding_chunk_size) + 1
            chunk_texts = texts[i : i + embedding_chunk_size]
            chunk_ids = ids[i : i + embedding_chunk_size]
            chunk_metadatas = metadatas[i : i + embedding_chunk_size]

            logger.info(f"Processing chunk {chunk_num}/{total_chunks} ({len(chunk_texts)} texts)")
            logger.info("Generating embeddings with OpenAI")
            embeddings = embeddings_model.embed_documents(chunk_texts)
            logger.info(f"Generated {len(embeddings)} embeddings")

            vector_tuples = zip(chunk_ids, embeddings, chunk_metadatas)

            # Runs the pinecone upsert asynchronously.
            logger.info("Upserting vectors to Pinecone")
            async_res = [
                index.upsert(
                    vectors=batch_vector_tuples,
                    namespace=namespace,
                    async_req=True,
                )
                for batch_vector_tuples in batch_iterate(32, vector_tuples)
            ]
            [res.get() for res in async_res]
            logger.info(f"Completed chunk {chunk_num}/{total_chunks}")

        logger.info("Waiting for namespace to be available in Pinecone index")
        while True:
            index_stats = index.describe_index_stats()
            if namespace in index_stats["namespaces"]:
                logger.info(f"Namespace '{namespace}' is now available in index")
                break
            logger.debug("Namespace not yet available, waiting...")
            time.sleep(1)

        # Clean up the uploaded file
        try:
            logger.info(f"Cleaning up uploaded file: {file_path}")
            os.remove(file_path)
            logger.info("File cleanup completed successfully")
        except Exception as e:
            logger.error(f"Error deleting file {file_path}: {e}")

        logger.info("Document processing and training completed successfully")

        result = {
            "status": "success",
            "message": "Data training completed successfully",
            "details": {
                "file_name": os.path.basename(file_path),
                "chunks_processed": len(texts),
                "namespace": namespace,
            }
        }
        logger.info(f"Returning result: {result}")
        return result

    except Exception as e:
        logger.error(f"Error during document processing: {str(e)}", exc_info=True)
        raise HTTPException(
            status_code=500,
            detail=f"Error during document processing: {str(e)}"
        )
