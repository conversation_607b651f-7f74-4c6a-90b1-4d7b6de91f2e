from sqlalchemy.orm import Session
from typing import List
import uuid
import os
import shutil
import logging
from fastapi import UploadFile
import asyncio
from concurrent.futures import ThreadPoolExecutor

from app.models.case_file import CaseFile, CaseFileCreate, CaseFileResponse, FileUploadResponse
from app.models.case import Case
from app.core.database import get_db

# Configure logging
logger = logging.getLogger(__name__)

# Optional import for embedding functionality
try:
    from app.services.embedpdf import process_and_train
    EMBEDDING_AVAILABLE = True
    logger.info("✓ Embedding functionality available")
except ImportError:
    EMBEDDING_AVAILABLE = False
    logger.warning("⚠️  Embedding functionality not available")

def run_embedding_process(file_path: str, namespace: str):
    """Run the embedding process"""
    import logging
    logger = logging.getLogger(__name__)
    
    try:
        logger.info(f"Starting embedding process in background thread for file: {file_path}")
        result = process_and_train(file_path, namespace)
        logger.info(f"Embedding process completed successfully: {result}")
        return result
    except Exception as e:
        logger.error(f"Error in background embedding process: {e}")
        raise e

class FileService:
    @staticmethod
    async def upload_file(
        db: Session, 
        case_id: str, 
        file: UploadFile, 
        user_id: str
    ) -> FileUploadResponse:
        """Upload a file to a case"""
        logger.info(f"Starting file upload for case {case_id}, file: {file.filename}")
        
        # Validate case exists and belongs to user
        case = db.query(Case).filter(
            Case.id == case_id,
            Case.user_id == user_id
        ).first()
        
        if not case:
            logger.error(f"Case {case_id} not found for user {user_id}")
            raise ValueError("Case not found")
        
        # Validate file type
        if not file.content_type == "application/pdf":
            logger.warning(f"Invalid file type: {file.content_type} for file {file.filename}")
            raise ValueError("Only PDF files are allowed")
        
        # Validate file size (50MB limit)
        if file.size > 50 * 1024 * 1024:
            logger.warning(f"File too large: {file.size} bytes for file {file.filename}")
            raise ValueError("File size must be less than 50MB")
        
        # Create upload directory if it doesn't exist
        upload_dir = f"uploads/cases/{case_id}"
        os.makedirs(upload_dir, exist_ok=True)
        logger.debug(f"Created upload directory: {upload_dir}")
        
        # Generate unique filename
        file_id = str(uuid.uuid4())
        file_extension = os.path.splitext(file.filename)[1]
        filename = f"{file_id}{file_extension}"
        file_path = os.path.join(upload_dir, filename)
        
        try:
            # Save file
            logger.info(f"Saving file to: {file_path}")
            with open(file_path, "wb") as buffer:
                shutil.copyfileobj(file.file, buffer)
            logger.info(f"File saved successfully: {file_path}")
            
            # Create file record in database
            logger.info("Creating file record in database")
            db_file = CaseFile(
                id=file_id,
                filename=filename,
                original_filename=file.filename,
                file_size=file.size,
                file_type=file.content_type,
                file_path=file_path,
                upload_status="completed",
                case_id=case_id,
                user_id=user_id
            )
            
            db.add(db_file)
            db.commit()
            db.refresh(db_file)
            logger.info(f"File record created in database with ID: {file_id}")
            
            # Start embedding process in background for PDF files
            if file.content_type == "application/pdf" and EMBEDDING_AVAILABLE:
                try:
                    logger.info(f"Starting embedding process for file: {file.filename}")
                    # Process embeddings in background with new database session
                    with ThreadPoolExecutor() as executor:
                        future = executor.submit(run_embedding_process, file_path, f"{case_id}_{file_id}")
                        # Don't wait for completion, let it run in background
                        logger.info(f"Embedding process started in background for file: {file.filename}")
                except Exception as embed_error:
                    logger.error(f"Error starting embedding process: {embed_error}")
                    # Don't fail the upload if embedding fails
            elif file.content_type == "application/pdf" and not EMBEDDING_AVAILABLE:
                logger.warning(f"PDF uploaded but embedding not available for: {file.filename}")

            logger.info(f"File upload completed successfully for: {file.filename}")
            return FileUploadResponse(
                file_id=file_id,
                filename=file.filename,
                status="completed",
                message="File uploaded successfully"
            )

        except Exception as e:
            logger.error(f"Error during file upload: {str(e)}")
            # Clean up file if database operation fails
            if os.path.exists(file_path):
                os.remove(file_path)
                logger.info(f"Cleaned up file after error: {file_path}")
            raise e
    
    @staticmethod
    def get_case_files(db: Session, case_id: str, user_id: str) -> List[CaseFileResponse]:
        """Get all files for a case"""
        # Validate case exists and belongs to user
        case = db.query(Case).filter(
            Case.id == case_id,
            Case.user_id == user_id
        ).first()
        
        if not case:
            raise ValueError("Case not found")
        
        files = db.query(CaseFile).filter(CaseFile.case_id == case_id).all()
        return [CaseFileResponse(**file.__dict__) for file in files]

    @staticmethod
    def get_case_file(db: Session, case_id: str, file_id: str, user_id: str) -> CaseFile:
        """Get a specific file from a case"""
        # Validate case exists and belongs to user
        case = db.query(Case).filter(
            Case.id == case_id,
            Case.user_id == user_id
        ).first()

        if not case:
            raise ValueError("Case not found")

        # Get the specific file
        file = db.query(CaseFile).filter(
            CaseFile.id == file_id,
            CaseFile.case_id == case_id
        ).first()

        if not file:
            raise ValueError("File not found")

        return file

    @staticmethod
    def delete_case_file(db: Session, case_id: str, file_id: str, user_id: str) -> bool:
        """Delete a file from a case"""
        logger.info(f"Deleting file {file_id} from case {case_id}")
        
        # Validate case exists and belongs to user
        case = db.query(Case).filter(
            Case.id == case_id,
            Case.user_id == user_id
        ).first()
        
        if not case:
            logger.error(f"Case {case_id} not found for user {user_id}")
            raise ValueError("Case not found")
        
        # Validate file exists and belongs to case
        file = db.query(CaseFile).filter(
            CaseFile.id == file_id,
            CaseFile.case_id == case_id
        ).first()
        
        if not file:
            logger.error(f"File {file_id} not found in case {case_id}")
            raise ValueError("File not found")
        
        # Delete file from storage
        try:
            if os.path.exists(file.file_path):
                os.remove(file.file_path)
                logger.info(f"Deleted file from storage: {file.file_path}")
            else:
                logger.warning(f"File not found in storage: {file.file_path}")
        except Exception as e:
            logger.error(f"Error deleting file {file.file_path}: {e}")
        
        # Delete file record
        db.delete(file)
        db.commit()
        logger.info(f"Deleted file record from database: {file_id}")
        
        return True
    
    @staticmethod
    def get_file_path(db: Session, file_id: str, user_id: str) -> str:
        """Get the file path for a specific file"""
        file = db.query(CaseFile).filter(
            CaseFile.id == file_id,
            CaseFile.user_id == user_id
        ).first()
        
        if not file:
            raise ValueError("File not found")
        
        if not os.path.exists(file.file_path):
            raise ValueError("File not found on disk")
        
        return file.file_path 