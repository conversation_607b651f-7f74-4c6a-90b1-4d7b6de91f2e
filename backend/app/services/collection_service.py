from sqlalchemy.orm import Session
from typing import List, Optional
import uuid
from datetime import datetime

from app.models.collection import Collection, CollectionCreate, CollectionUpdate, CollectionResponse, TreeNode
from app.models.case import Case

class CollectionService:
    @staticmethod
    def create_collection(
        db: Session, 
        collection_data: CollectionCreate, 
        user_id: str
    ) -> CollectionResponse:
        """Create a new collection"""
        # Validate parent collection exists if provided
        if collection_data.parent_id:
            parent = db.query(Collection).filter(
                Collection.id == collection_data.parent_id,
                Collection.user_id == user_id
            ).first()
            if not parent:
                raise ValueError("Parent collection not found")
        
        # Create collection
        collection_id = str(uuid.uuid4())
        db_collection = Collection(
            id=collection_id,
            user_id=user_id,
            **collection_data.dict()
        )
        
        db.add(db_collection)
        db.commit()
        db.refresh(db_collection)
        
        return CollectionResponse(
            **db_collection.__dict__,
            children_count=0,
            cases_count=0
        )
    
    @staticmethod
    def get_collections(db: Session, user_id: str) -> List[CollectionResponse]:
        """Get all collections for a user"""
        collections = db.query(Collection).filter(Collection.user_id == user_id).all()
        
        result = []
        for collection in collections:
            children_count = db.query(Collection).filter(Collection.parent_id == collection.id).count()
            cases_count = db.query(Case).filter(Case.collection_id == collection.id).count()
            result.append(CollectionResponse(
                **collection.__dict__,
                children_count=children_count,
                cases_count=cases_count
            ))
        
        return result
    
    @staticmethod
    def get_collection(db: Session, collection_id: str, user_id: str) -> CollectionResponse:
        """Get a specific collection by ID"""
        collection = db.query(Collection).filter(
            Collection.id == collection_id,
            Collection.user_id == user_id
        ).first()
        
        if not collection:
            raise ValueError("Collection not found")
        
        children_count = db.query(Collection).filter(Collection.parent_id == collection_id).count()
        cases_count = db.query(Case).filter(Case.collection_id == collection_id).count()
        
        return CollectionResponse(
            **collection.__dict__,
            children_count=children_count,
            cases_count=cases_count
        )
    
    @staticmethod
    def update_collection(
        db: Session, 
        collection_id: str, 
        collection_data: CollectionUpdate, 
        user_id: str
    ) -> CollectionResponse:
        """Update a collection"""
        collection = db.query(Collection).filter(
            Collection.id == collection_id,
            Collection.user_id == user_id
        ).first()
        
        if not collection:
            raise ValueError("Collection not found")
        
        # Validate parent collection exists if provided
        if collection_data.parent_id:
            parent = db.query(Collection).filter(
                Collection.id == collection_data.parent_id,
                Collection.user_id == user_id
            ).first()
            if not parent:
                raise ValueError("Parent collection not found")
        
        # Update collection
        update_data = collection_data.dict(exclude_unset=True)
        for field, value in update_data.items():
            setattr(collection, field, value)
        
        collection.updated_at = datetime.utcnow()
        db.commit()
        db.refresh(collection)
        
        children_count = db.query(Collection).filter(Collection.parent_id == collection_id).count()
        cases_count = db.query(Case).filter(Case.collection_id == collection_id).count()
        
        return CollectionResponse(
            **collection.__dict__,
            children_count=children_count,
            cases_count=cases_count
        )
    
    @staticmethod
    def delete_collection(db: Session, collection_id: str, user_id: str) -> bool:
        """Delete a collection and all its children and cases"""
        collection = db.query(Collection).filter(
            Collection.id == collection_id,
            Collection.user_id == user_id
        ).first()
        
        if not collection:
            raise ValueError("Collection not found")
        
        # Delete collection (children and cases will be deleted due to cascade)
        db.delete(collection)
        db.commit()
        
        return True
    
    @staticmethod
    def get_tree_structure(db: Session, user_id: str) -> List[TreeNode]:
        """Get the complete tree structure of collections and cases"""
        def build_tree(parent_id=None):
            nodes = []
            
            # Get collections at this level
            collections = db.query(Collection).filter(
                Collection.user_id == user_id,
                Collection.parent_id == parent_id
            ).all()
            
            for collection in collections:

                # Get children recursively
                children = build_tree(collection.id)
                
                # Get cases in this collection
                cases = db.query(Case).filter(Case.collection_id == collection.id).all()
                for case in cases:
                    children.append(TreeNode(
                        id=case.id,
                        name=case.case_name,
                        type="case",
                        expanded=False,
                        parent_id=collection.id,
                        case_data={
                            "internal_case_number": case.internal_case_number,
                            "date_received_from_carrier": case.date_received_from_carrier.isoformat() if case.date_received_from_carrier else None
                        }
                    ))
                
                nodes.append(TreeNode(
                    id=collection.id,
                    name=collection.name,
                    type="collection",
                    expanded=collection.is_expanded,
                    children=children,
                    parent_id=parent_id
                ))
            
            # If at root level, also get cases without collection
            if parent_id is None:
                root_cases = db.query(Case).filter(
                    Case.user_id == user_id,
                    Case.collection_id.is_(None)
                ).all()
                
                for case in root_cases:
                    nodes.append(TreeNode(
                        id=case.id,
                        name=case.case_name,
                        type="case",
                        expanded=False,
                        parent_id=None,
                        case_data={
                            "internal_case_number": case.internal_case_number,
                            "date_received_from_carrier": case.date_received_from_carrier.isoformat() if case.date_received_from_carrier else None
                        }
                    ))
            
            return nodes
        
        return build_tree() 