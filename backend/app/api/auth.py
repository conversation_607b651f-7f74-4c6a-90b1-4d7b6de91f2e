from fastapi import APIRouter, HTTPException, Depends, Request
from pydantic import EmailStr, BaseModel
from sqlalchemy.orm import Session
from app.models.user import UserC<PERSON>, UserInDB, User
from app.core.security import hash_password, verify_password, create_access_token
from app.services.email import send_verification_email
from app.core.config import settings
from app.core.database import get_db
from datetime import timedelta
import uuid

router = APIRouter()

# In-memory verification tokens store (for demo purposes)
verification_tokens = {}

class LoginRequest(BaseModel):
    email: EmailStr
    password: str

@router.post('/register')
def register(user: UserC<PERSON>, request: Request, db: Session = Depends(get_db)):
    # Check if user already exists
    existing_user = db.query(User).filter(User.email == user.email).first()
    if existing_user:
        raise HTTPException(status_code=400, detail='Email already registered')
    
    # Create new user
    hashed = hash_password(user.password)
    user_id = str(uuid.uuid4())
    
    db_user = User(
        id=user_id,
        email=user.email,
        hashed_password=hashed,
        is_verified=False
    )
    
    db.add(db_user)
    db.commit()
    db.refresh(db_user)
    
    # Generate verification token
    token = str(uuid.uuid4())
    verification_tokens[token] = user.email
    verify_link = f"{request.base_url}auth/verify?token={token}"
    send_verification_email(user.email, verify_link)
    
    return {"msg": "Registration successful. Please check your email to verify your account."}

@router.get('/verify')
def verify_email(token: str, db: Session = Depends(get_db)):
    email = verification_tokens.pop(token, None)
    if not email:
        raise HTTPException(status_code=400, detail='Invalid or expired verification token')
    
    user = db.query(User).filter(User.email == email).first()
    if not user:
        raise HTTPException(status_code=400, detail='User not found')
    
    user.is_verified = True
    db.commit()
    
    return {"msg": "Email verified. You can now log in."}

@router.post('/login')
def login(data: LoginRequest, db: Session = Depends(get_db)):
    email = data.email
    password = data.password
    user = db.query(User).filter(User.email == email).first()
    if not user or not verify_password(password, user.hashed_password):
        raise HTTPException(status_code=401, detail='Invalid credentials')
    # if not user.is_verified:
    #     raise HTTPException(status_code=403, detail='Email not verified')
    
    access_token = create_access_token({"sub": user.email}, timedelta(minutes=settings.JWT_EXPIRE_MINUTES))
    return {"access_token": access_token, "token_type": "bearer"} 