from fastapi import APIRouter, Depends, HTTPException, UploadFile, File, Request
from sqlalchemy.orm import Session
from typing import List, Optional
import os
import shutil

from app.core.database import get_db
from app.core.security import decode_access_token
from app.models.case import CaseC<PERSON>, CaseUpdate, CaseResponse, CaseWithProcessingStatus
from app.models.collection import CollectionCreate, CollectionUpdate, CollectionResponse, TreeNode
from app.models.case_file import CaseFileResponse, FileUploadResponse
from app.models.user import User
from app.services.case_service import CaseService
from app.services.collection_service import CollectionService
from app.services.file_service import FileService

# Optional import for embedding functionality
try:
    from app.services.embedpdf import process_and_train
    EMBEDDING_AVAILABLE = True
except ImportError:
    EMBEDDING_AVAILABLE = False

router = APIRouter()

def get_current_user(authorization: str = Depends(lambda x: x.headers.get('Authorization'))):
    if not authorization or not authorization.startswith('Bearer '):
        raise HTTPException(status_code=401, detail='Invalid auth header')
    token = authorization.split(' ')[1]
    payload = decode_access_token(token)
    if not payload:
        raise HTTPException(status_code=401, detail='Invalid or expired token')
    return payload['sub']

def get_user_by_id(db: Session, user_id: str) -> User:
    user = db.query(User).filter(User.id == user_id).first()
    if not user:
        raise HTTPException(status_code=404, detail="User not found")
    return user

# Case Management Endpoints
@router.post("/cases", response_model=CaseResponse)
def create_case(
    case_data: CaseCreate,
    db: Session = Depends(get_db)
):
    """Create a new case"""
    try:
        # For testing purposes, use a mock user ID
        mock_user_id = "test-user-123"
        return CaseService.create_case(db, case_data, mock_user_id)
    except ValueError as e:
        raise HTTPException(status_code=400, detail=str(e))
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Internal server error: {str(e)}")

@router.get("/cases", response_model=List[CaseResponse])
def get_cases(
    skip: int = 0,
    limit: int = 100,
    collection_id: Optional[str] = None,
    current_user_id: str = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Get all cases for the current user"""
    try:
        return CaseService.get_cases(db, current_user_id, skip, limit, collection_id)
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Internal server error: {str(e)}")

# Get all cases with processing status
@router.get("/cases/with-status", response_model=List[CaseWithProcessingStatus])
def get_cases_with_status(
    skip: int = 0,
    limit: int = 100,
    db: Session = Depends(get_db)
):
    """Get all cases with document processing status"""
    try:
        # For testing purposes, use a mock user ID
        mock_user_id = "test-user-123"
        return CaseService.get_all_cases_with_status(db, mock_user_id, skip, limit)
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Internal server error: {str(e)}")

@router.get("/cases/{case_id}", response_model=CaseResponse)
def get_case(
    case_id: str,
    current_user_id: str = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Get a specific case by ID"""
    try:
        return CaseService.get_case(db, case_id, current_user_id)
    except ValueError as e:
        raise HTTPException(status_code=404, detail=str(e))
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Internal server error: {str(e)}")

@router.put("/cases/{case_id}", response_model=CaseResponse)
def update_case(
    case_id: str,
    case_data: CaseUpdate,
    current_user_id: str = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Update a case"""
    try:
        return CaseService.update_case(db, case_id, case_data, current_user_id)
    except ValueError as e:
        raise HTTPException(status_code=400, detail=str(e))
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Internal server error: {str(e)}")

@router.delete("/cases/{case_id}")
def delete_case(
    case_id: str,
    current_user_id: str = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Delete a case and all its files"""
    try:
        CaseService.delete_case(db, case_id, current_user_id)
        return {"message": "Case deleted successfully"}
    except ValueError as e:
        raise HTTPException(status_code=404, detail=str(e))
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Internal server error: {str(e)}")

# Collection Management Endpoints
@router.post("/collections", response_model=CollectionResponse)
def create_collection(
    collection_data: CollectionCreate,
    db: Session = Depends(get_db)
):
    """Create a new collection"""
    try:
        # For testing purposes, use a mock user ID
        mock_user_id = "test-user-123"
        return CollectionService.create_collection(db, collection_data, mock_user_id)
    except ValueError as e:
        raise HTTPException(status_code=400, detail=str(e))
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Internal server error: {str(e)}")

@router.get("/collections", response_model=List[CollectionResponse])
def get_collections(
    current_user_id: str = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Get all collections for the current user"""
    try:
        return CollectionService.get_collections(db, current_user_id)
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Internal server error: {str(e)}")

@router.get("/collections/{collection_id}", response_model=CollectionResponse)
def get_collection(
    collection_id: str,
    current_user_id: str = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Get a specific collection by ID"""
    try:
        return CollectionService.get_collection(db, collection_id, current_user_id)
    except ValueError as e:
        raise HTTPException(status_code=404, detail=str(e))
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Internal server error: {str(e)}")

@router.put("/collections/{collection_id}", response_model=CollectionResponse)
def update_collection(
    collection_id: str,
    collection_data: CollectionUpdate,
    current_user_id: str = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Update a collection"""
    try:
        return CollectionService.update_collection(db, collection_id, collection_data, current_user_id)
    except ValueError as e:
        raise HTTPException(status_code=400, detail=str(e))
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Internal server error: {str(e)}")

@router.delete("/collections/{collection_id}")
def delete_collection(
    collection_id: str,
    current_user_id: str = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Delete a collection and all its children and cases"""
    try:
        CollectionService.delete_collection(db, collection_id, current_user_id)
        return {"message": "Collection deleted successfully"}
    except ValueError as e:
        raise HTTPException(status_code=404, detail=str(e))
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Internal server error: {str(e)}")

# Tree Structure Endpoint
@router.get("/tree", response_model=List[TreeNode])
def get_tree_structure(
    db: Session = Depends(get_db)
):
    """Get the complete tree structure of collections and cases"""
    try:
        # For testing purposes, use a mock user ID
        # In production, this should use proper authentication
        mock_user_id = "test-user-123"
        return CollectionService.get_tree_structure(db, mock_user_id)
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Internal server error: {str(e)}")

# File Upload Endpoints
@router.post("/cases/{case_id}/files", response_model=FileUploadResponse)
async def upload_file(
    case_id: str,
    file: UploadFile = File(...),
    db: Session = Depends(get_db)
):
    """Upload a file to a case"""
    try:
        # For testing purposes, use a mock user ID
        mock_user_id = "test-user-123"
        return await FileService.upload_file(db, case_id, file, mock_user_id)
    except ValueError as e:
        raise HTTPException(status_code=400, detail=str(e))
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Upload failed: {str(e)}")

@router.get("/cases/{case_id}/files", response_model=List[CaseFileResponse])
def get_case_files(
    case_id: str,
    db: Session = Depends(get_db)
):
    """Get all files for a case"""
    try:
        # For testing purposes, use a mock user ID
        mock_user_id = "test-user-123"
        return FileService.get_case_files(db, case_id, mock_user_id)
    except ValueError as e:
        raise HTTPException(status_code=404, detail=str(e))
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Internal server error: {str(e)}")

@router.delete("/cases/{case_id}/files/{file_id}")
def delete_case_file(
    case_id: str,
    file_id: str,
    db: Session = Depends(get_db)
):
    """Delete a file from a case"""
    try:
        # For testing purposes, use a mock user ID
        mock_user_id = "test-user-123"
        FileService.delete_case_file(db, case_id, file_id, mock_user_id)
        return {"message": "File deleted successfully"}
    except ValueError as e:
        raise HTTPException(status_code=404, detail=str(e))
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Internal server error: {str(e)}")

# Test endpoint for file upload
@router.post("/test-upload")
async def test_upload(file: UploadFile = File(...)):
    """Test endpoint for file upload"""
    try:
        # Basic validation
        if not file.content_type == "application/pdf":
            raise HTTPException(status_code=400, detail="Only PDF files are allowed")

        if file.size > 50 * 1024 * 1024:
            raise HTTPException(status_code=400, detail="File size must be less than 50MB")

        # Create test directory
        test_dir = "uploads/test"
        os.makedirs(test_dir, exist_ok=True)

        # Save file
        file_path = os.path.join(test_dir, file.filename)
        with open(file_path, "wb") as buffer:
            shutil.copyfileobj(file.file, buffer)

        return {
            "message": "File uploaded successfully",
            "filename": file.filename,
            "size": file.size,
            "content_type": file.content_type,
            "path": file_path
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Upload failed: {str(e)}")

# Move case between collections
@router.put("/cases/{case_id}/move")
async def move_case(
    case_id: str,
    request: Request,
    db: Session = Depends(get_db)
):
    """Move a case to a different collection"""

    # Debug: Check what's in the request
    content_type = request.headers.get("content-type", "")

    # Parse form data manually to debug
    form_data = await request.form()

    collection_id = form_data.get("collection_id")

    # Handle empty string as None (move to root)
    if collection_id == '':
        collection_id = None

    try:
        # For testing purposes, use a mock user ID
        mock_user_id = "test-user-123"
        result = CaseService.move_case(db, case_id, collection_id, mock_user_id)
        return {"message": "Case moved successfully"}
    except ValueError as e:
        raise HTTPException(status_code=400, detail=str(e))
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Internal server error: {str(e)}")

# Embedding Endpoints
@router.post("/cases/{case_id}/files/{file_id}/embed")
async def embed_file(
    case_id: str,
    file_id: str,
    db: Session = Depends(get_db)
):
    """Manually trigger embedding for a specific file"""
    if not EMBEDDING_AVAILABLE:
        raise HTTPException(
            status_code=503,
            detail="Embedding functionality is not available. Please install required dependencies."
        )

    try:
        # For testing purposes, use a mock user ID
        mock_user_id = "test-user-123"

        # Get the file from database
        file_record = FileService.get_case_file(db, case_id, file_id, mock_user_id)
        if not file_record:
            raise HTTPException(status_code=404, detail="File not found")

        # Check if file exists on disk
        if not os.path.exists(file_record.file_path):
            raise HTTPException(status_code=404, detail="File not found on disk")

        # Process embeddings
        result = process_and_train(file_record.file_path, f"{case_id}_{file_id}")

        return {
            "message": "Embedding process completed successfully",
            "file_id": file_id,
            "result": result
        }

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error processing embeddings: {str(e)}")