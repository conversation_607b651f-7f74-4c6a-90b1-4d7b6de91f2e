from fastapi import APIRouter, Depends, HTTPException, Header
from app.core.security import decode_access_token

router = APIRouter()

def get_current_user(authorization: str = Header(...)):
    if not authorization.startswith('Bearer '):
        raise HTTPException(status_code=401, detail='Invalid auth header')
    token = authorization.split(' ')[1]
    payload = decode_access_token(token)
    if not payload:
        raise HTTPException(status_code=401, detail='Invalid or expired token')
    return payload['sub']

@router.get('/protected')
def protected_route(current_user: str = Depends(get_current_user)):
    return {"msg": f"Hello, {current_user}. This is a protected route."} 