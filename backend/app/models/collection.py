from pydantic import BaseModel
from datetime import datetime
from typing import Optional, List
from sqlalchemy import Column, String, DateTime, ForeignKey, Boolean
from sqlalchemy.sql import func
from sqlalchemy.orm import relationship
from app.core.database import Base

# SQLAlchemy ORM Model
class Collection(Base):
    __tablename__ = "collections"

    id = Column(String, primary_key=True, index=True)
    name = Column(String, nullable=False)
    description = Column(String, nullable=True)
    is_expanded = Column(Boolean, default=False)
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())
    
    # Foreign key to user who created the collection
    user_id = Column(String, ForeignKey("users.id"), nullable=False)
    
    # Foreign key to parent collection (for hierarchical structure)
    parent_id = Column(String, ForeignKey("collections.id"), nullable=True)
    
    # Relationships
    user = relationship("User", back_populates="collections")
    parent = relationship("Collection", remote_side=[id], back_populates="children")
    children = relationship("Collection", back_populates="parent", cascade="all, delete-orphan")
    cases = relationship("Case", back_populates="collection", cascade="all, delete-orphan")

# Pydantic Models for API
class CollectionBase(BaseModel):
    name: str
    description: Optional[str] = None
    is_expanded: bool = False
    parent_id: Optional[str] = None

class CollectionCreate(CollectionBase):
    pass

class CollectionUpdate(BaseModel):
    name: Optional[str] = None
    description: Optional[str] = None
    is_expanded: Optional[bool] = None
    parent_id: Optional[str] = None

class CollectionResponse(CollectionBase):
    id: str
    user_id: str
    created_at: datetime
    updated_at: Optional[datetime] = None
    children_count: int = 0
    cases_count: int = 0

    class Config:
        from_attributes = True

class TreeNode(BaseModel):
    id: str
    name: str
    type: str  # 'collection' or 'case'
    expanded: bool = False
    children: Optional[List['TreeNode']] = None
    parent_id: Optional[str] = None
    case_data: Optional[dict] = None  # For case nodes

    class Config:
        from_attributes = True

# Allow forward references for TreeNode
TreeNode.model_rebuild() 