from pydantic import BaseModel, EmailStr
from datetime import datetime
from typing import Optional, List
from sqlalchemy import Column, String, Boolean, DateTime
from sqlalchemy.sql import func
from sqlalchemy.orm import relationship
from app.core.database import Base

# SQLAlchemy ORM Model
class User(Base):
    __tablename__ = "users"

    id = Column(String, primary_key=True, index=True)
    email = Column(String, unique=True, index=True)
    hashed_password = Column(String)
    is_verified = Column(Boolean, default=False)
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    
    # Relationships
    cases = relationship("Case", back_populates="user")
    collections = relationship("Collection", back_populates="user")
    uploaded_files = relationship("CaseFile", back_populates="user")

# Pydantic Models for API
class UserBase(BaseModel):
    email: EmailStr
    is_verified: bool = False
    created_at: Optional[datetime] = None

class UserCreate(UserBase):
    password: str

class UserInDB(UserBase):
    id: str
    hashed_password: str

    class Config:
        from_attributes = True 