from sqlalchemy import Column, String, DateTime, func
from sqlalchemy.ext.declarative import declarative_base
from pydantic import BaseModel
from datetime import datetime
from typing import Optional

from app.core.database import Base

# SQLAlchemy ORM Model
class Namespace(Base):
    __tablename__ = "namespaces"

    id = Column(String, primary_key=True, index=True)
    namespacename = Column(String, nullable=False, index=True)
    documenttitle = Column(String, nullable=False)
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())

# Pydantic Models for API
class NamespaceBase(BaseModel):
    namespacename: str
    documenttitle: str

class NamespaceCreate(NamespaceBase):
    pass

class NamespaceUpdate(BaseModel):
    namespacename: Optional[str] = None
    documenttitle: Optional[str] = None

class NamespaceResponse(NamespaceBase):
    id: str
    created_at: datetime
    updated_at: Optional[datetime] = None

    class Config:
        from_attributes = True
