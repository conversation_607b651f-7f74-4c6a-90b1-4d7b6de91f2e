import os
from pydantic_settings import BaseSettings

class Settings(BaseSettings):
    JWT_SECRET_KEY: str = os.getenv('JWT_SECRET_KEY', 'supersecret')
    JWT_ALGORITHM: str = 'HS256'
    JWT_EXPIRE_MINUTES: int = 30
    AWS_SES_REGION: str = os.getenv('AWS_SES_REGION', 'us-east-1')
    AWS_SES_ACCESS_KEY: str = os.getenv('AWS_SES_ACCESS_KEY', '')
    AWS_SES_SECRET_KEY: str = os.getenv('AWS_SES_SECRET_KEY', '')
    EMAIL_FROM: str = os.getenv('EMAIL_FROM', '<EMAIL>')
    # OpenAI settings
    OPENAI_API_KEY: str = os.getenv('OPENAI_API_KEY', '********************************************************************************************************************************************************************')
    OPENAI_MODEL_NAME: str = os.getenv('OPENAI_MODEL_NAME', 'gpt-4o')
    # PineCone settings
    PINECONE_API_KEY: str = os.getenv('PINECONE_API_KEY', 'pcsk_41qjRR_QDPBLixnEBebgeBhA2kX86cgF7be3pJXq6irgjhzRv9t6d36WGD2GknBzGZahd2')
    PINECONE_INDEX: str = os.getenv('PINECONE_INDEX', 'docreview')
    # Database settings
    DATABASE_URL: str = os.getenv('DATABASE_URL', '*********************************************/DocReview')

settings = Settings() 