#!/usr/bin/env python3
"""
Test script to verify the tiktoken fix works.
"""

import os
import sys
import logging

# Add the backend directory to the Python path
sys.path.insert(0, os.path.join(os.path.dirname(__file__)))

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def test_tiktoken_fix():
    """Test that tiktoken initialization works without errors."""
    try:
        from app.services.embedpdf import initialize_tiktoken_with_retry, EMBEDDING_DEPENDENCIES_AVAILABLE
        
        if not EMBEDDING_DEPENDENCIES_AVAILABLE:
            logger.error("Embedding dependencies not available")
            return False
            
        logger.info("Testing tiktoken initialization with fix...")
        
        # This should not raise an exception anymore
        result = initialize_tiktoken_with_retry()
        
        if result is not None:
            logger.info("✓ Tiktoken initialization successful")
            return True
        else:
            logger.info("✓ Tiktoken initialization gracefully handled (returned None)")
            return True
            
    except Exception as e:
        logger.error(f"✗ Error testing tiktoken: {str(e)}")
        return False

def test_openai_embeddings_simple():
    """Test simple OpenAI embeddings without tiktoken pre-initialization."""
    try:
        from app.core.config import settings
        from langchain_openai import OpenAIEmbeddings
        
        logger.info("Testing OpenAI embeddings directly...")
        
        # Initialize embeddings with robust settings
        embeddings_model = OpenAIEmbeddings(
            openai_api_key=settings.OPENAI_API_KEY,
            max_retries=5,
            request_timeout=120,
            retry_min_seconds=1,
            retry_max_seconds=60
        )
        
        # Test with a simple text
        test_texts = ["Simple test for embedding."]
        logger.info("Generating embeddings...")
        
        embeddings = embeddings_model.embed_documents(test_texts)
        
        if embeddings and len(embeddings) > 0:
            logger.info(f"✓ Successfully generated embeddings: {len(embeddings)} vectors")
            return True
        else:
            logger.error("✗ Failed to generate embeddings")
            return False
            
    except Exception as e:
        logger.error(f"✗ Error testing OpenAI embeddings: {str(e)}")
        return False

def main():
    """Run the tests."""
    logger.info("Starting tiktoken fix verification...")
    
    tests = [
        ("Tiktoken Fix", test_tiktoken_fix),
        ("OpenAI Embeddings", test_openai_embeddings_simple),
    ]
    
    results = []
    for test_name, test_func in tests:
        logger.info(f"\n{'='*40}")
        logger.info(f"Running test: {test_name}")
        logger.info(f"{'='*40}")
        
        try:
            result = test_func()
            results.append((test_name, result))
            if result:
                logger.info(f"✓ {test_name}: PASSED")
            else:
                logger.error(f"✗ {test_name}: FAILED")
        except Exception as e:
            logger.error(f"✗ {test_name}: ERROR - {str(e)}")
            results.append((test_name, False))
    
    # Summary
    logger.info(f"\n{'='*40}")
    logger.info("TEST SUMMARY")
    logger.info(f"{'='*40}")
    
    passed = sum(1 for _, result in results if result)
    total = len(results)
    
    for test_name, result in results:
        status = "PASSED" if result else "FAILED"
        logger.info(f"{test_name}: {status}")
    
    logger.info(f"\nOverall: {passed}/{total} tests passed")
    
    if passed == total:
        logger.info("🎉 All tests passed! The tiktoken fix is working.")
        return 0
    else:
        logger.error("❌ Some tests failed.")
        return 1

if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
