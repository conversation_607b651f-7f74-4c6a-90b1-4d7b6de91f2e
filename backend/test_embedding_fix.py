#!/usr/bin/env python3
"""
Test script to verify the embedding functionality with retry logic.
This script tests the tiktoken initialization and OpenAI embeddings.
"""

import os
import sys
import logging

# Add the backend directory to the Python path
sys.path.insert(0, os.path.join(os.path.dirname(__file__)))

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def test_tiktoken_initialization():
    """Test tiktoken initialization with retry logic."""
    try:
        from app.services.embedpdf import initialize_tiktoken_with_retry, EMBEDDING_DEPENDENCIES_AVAILABLE
        
        if not EMBEDDING_DEPENDENCIES_AVAILABLE:
            logger.error("Embedding dependencies not available")
            return False
            
        logger.info("Testing tiktoken initialization...")
        encoding = initialize_tiktoken_with_retry()
        
        if encoding:
            logger.info("✓ Tiktoken initialization successful")
            # Test encoding a simple text
            test_text = "This is a test text for encoding."
            tokens = encoding.encode(test_text)
            logger.info(f"✓ Successfully encoded text into {len(tokens)} tokens")
            return True
        else:
            logger.error("✗ Tiktoken initialization failed")
            return False
            
    except Exception as e:
        logger.error(f"✗ Error testing tiktoken: {str(e)}")
        return False

def test_openai_embeddings():
    """Test OpenAI embeddings initialization."""
    try:
        from app.services.embedpdf import EMBEDDING_DEPENDENCIES_AVAILABLE
        from app.core.config import settings
        
        if not EMBEDDING_DEPENDENCIES_AVAILABLE:
            logger.error("Embedding dependencies not available")
            return False
            
        from langchain_openai import OpenAIEmbeddings
        
        logger.info("Testing OpenAI embeddings initialization...")
        embeddings_model = OpenAIEmbeddings(
            openai_api_key=settings.OPENAI_API_KEY,
            max_retries=3,
            request_timeout=60
        )
        
        # Test with a simple text
        test_texts = ["This is a test document for embedding."]
        logger.info("Testing embedding generation...")
        embeddings = embeddings_model.embed_documents(test_texts)
        
        if embeddings and len(embeddings) > 0:
            logger.info(f"✓ Successfully generated embeddings: {len(embeddings)} vectors of dimension {len(embeddings[0])}")
            return True
        else:
            logger.error("✗ Failed to generate embeddings")
            return False
            
    except Exception as e:
        logger.error(f"✗ Error testing OpenAI embeddings: {str(e)}")
        return False

def test_pinecone_connection():
    """Test Pinecone connection."""
    try:
        from app.services.embedpdf import EMBEDDING_DEPENDENCIES_AVAILABLE
        from app.core.config import settings
        
        if not EMBEDDING_DEPENDENCIES_AVAILABLE:
            logger.error("Embedding dependencies not available")
            return False
            
        from pinecone import Pinecone
        
        logger.info("Testing Pinecone connection...")
        pc = Pinecone(api_key=settings.PINECONE_API_KEY)
        
        # List existing indexes
        existing_indexes = [index_info["name"] for index_info in pc.list_indexes()]
        logger.info(f"✓ Successfully connected to Pinecone. Existing indexes: {existing_indexes}")
        
        # Check if our index exists
        if settings.PINECONE_INDEX in existing_indexes:
            logger.info(f"✓ Target index '{settings.PINECONE_INDEX}' exists")
            
            # Test index connection
            index = pc.Index(settings.PINECONE_INDEX)
            stats = index.describe_index_stats()
            logger.info(f"✓ Index stats: {stats}")
            return True
        else:
            logger.warning(f"⚠️  Target index '{settings.PINECONE_INDEX}' does not exist")
            return True  # Still consider this a success for connection test
            
    except Exception as e:
        logger.error(f"✗ Error testing Pinecone connection: {str(e)}")
        return False

def main():
    """Run all tests."""
    logger.info("Starting embedding functionality tests...")
    
    tests = [
        ("Tiktoken Initialization", test_tiktoken_initialization),
        ("OpenAI Embeddings", test_openai_embeddings),
        ("Pinecone Connection", test_pinecone_connection),
    ]
    
    results = []
    for test_name, test_func in tests:
        logger.info(f"\n{'='*50}")
        logger.info(f"Running test: {test_name}")
        logger.info(f"{'='*50}")
        
        try:
            result = test_func()
            results.append((test_name, result))
            if result:
                logger.info(f"✓ {test_name}: PASSED")
            else:
                logger.error(f"✗ {test_name}: FAILED")
        except Exception as e:
            logger.error(f"✗ {test_name}: ERROR - {str(e)}")
            results.append((test_name, False))
    
    # Summary
    logger.info(f"\n{'='*50}")
    logger.info("TEST SUMMARY")
    logger.info(f"{'='*50}")
    
    passed = sum(1 for _, result in results if result)
    total = len(results)
    
    for test_name, result in results:
        status = "PASSED" if result else "FAILED"
        logger.info(f"{test_name}: {status}")
    
    logger.info(f"\nOverall: {passed}/{total} tests passed")
    
    if passed == total:
        logger.info("🎉 All tests passed! Embedding functionality should work correctly.")
        return 0
    else:
        logger.error("❌ Some tests failed. Please check the errors above.")
        return 1

if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
