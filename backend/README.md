# Insurance Claim Case Management Backend

A FastAPI-based backend for managing insurance claim cases, collections, and file uploads.

## Features

- **Case Management**: Create, read, update, and delete insurance claim cases
- **Collection Management**: Organize cases into hierarchical collections (folders)
- **File Upload**: Upload and manage PDF documents for cases
- **Tree Structure**: Get hierarchical view of collections and cases
- **User Authentication**: JWT-based authentication system
- **File Storage**: Local file storage with organized directory structure

## Database Schema

### Tables

1. **users** - User accounts and authentication
2. **cases** - Insurance claim cases with metadata
3. **collections** - Hierarchical folders for organizing cases
4. **case_files** - PDF files associated with cases

### Relationships

- Users can have multiple cases and collections
- Collections can have parent collections (hierarchical structure)
- Cases can belong to collections
- Cases can have multiple files

## API Endpoints

### Authentication

All endpoints require JWT authentication via Bearer token in the Authorization header.

### Case Management

#### Create Case
```
POST /api/cases
Content-Type: application/json
Authorization: Bearer <token>

{
  "internal_case_number": "12345-67890",
  "case_name": "<PERSON> vs Insurance Co",
  "date_of_loss": "2024-01-15",
  "date_of_lawsuit": "2024-02-01",
  "date_of_noc": "2024-01-20",
  "date_received_from_carrier": "2024-01-10",
  "collection_id": "optional-collection-id"
}
```

#### Get All Cases
```
GET /api/cases?skip=0&limit=100&collection_id=optional
Authorization: Bearer <token>
```

#### Get Case by ID
```
GET /api/cases/{case_id}
Authorization: Bearer <token>
```

#### Update Case
```
PUT /api/cases/{case_id}
Content-Type: application/json
Authorization: Bearer <token>

{
  "case_name": "Updated Case Name",
  "date_received_from_carrier": "2024-01-12"
}
```

#### Delete Case
```
DELETE /api/cases/{case_id}
Authorization: Bearer <token>
```

#### Move Case
```
PUT /api/cases/{case_id}/move
Content-Type: application/x-www-form-urlencoded
Authorization: Bearer <token>

collection_id=target-collection-id
```

### Collection Management

#### Create Collection
```
POST /api/collections
Content-Type: application/json
Authorization: Bearer <token>

{
  "name": "Medical Records",
  "description": "Collection for medical documents",
  "parent_id": "optional-parent-collection-id"
}
```

#### Get All Collections
```
GET /api/collections
Authorization: Bearer <token>
```

#### Get Collection by ID
```
GET /api/collections/{collection_id}
Authorization: Bearer <token>
```

#### Update Collection
```
PUT /api/collections/{collection_id}
Content-Type: application/json
Authorization: Bearer <token>

{
  "name": "Updated Collection Name",
  "is_expanded": true
}
```

#### Delete Collection
```
DELETE /api/collections/{collection_id}
Authorization: Bearer <token>
```

### Tree Structure

#### Get Tree Structure
```
GET /api/tree
Authorization: Bearer <token>
```

Returns hierarchical structure of collections and cases:
```json
[
  {
    "id": "collection-1",
    "name": "Medical Records",
    "type": "collection",
    "expanded": true,
    "children": [
      {
        "id": "case-1",
        "name": "John Doe Case",
        "type": "case",
        "expanded": false,
        "case_data": {
          "internal_case_number": "12345-67890",
          "date_received_from_carrier": "2024-01-10"
        }
      }
    ]
  }
]
```

### File Management

#### Upload File
```
POST /api/cases/{case_id}/files
Content-Type: multipart/form-data
Authorization: Bearer <token>

file: <PDF file>
```

#### Get Case Files
```
GET /api/cases/{case_id}/files
Authorization: Bearer <token>
```

#### Delete File
```
DELETE /api/cases/{case_id}/files/{file_id}
Authorization: Bearer <token>
```

## Setup Instructions

### Prerequisites

- Python 3.8+
- PostgreSQL database
- Virtual environment (recommended)

### Installation

1. **Clone the repository**
   ```bash
   cd backend
   ```

2. **Create virtual environment**
   ```bash
   python -m venv .venv
   source .venv/bin/activate  # On Windows: .venv\Scripts\activate
   ```

3. **Install dependencies**
   ```bash
   pip install -r requirements.txt
   ```

4. **Configure environment variables**
   Create a `.env` file in the backend directory:
   ```env
   DATABASE_URL=postgresql://username:password@localhost:5432/database_name
   JWT_SECRET_KEY=your-secret-key
   JWT_ALGORITHM=HS256
   JWT_EXPIRE_MINUTES=30
   ```

5. **Run database migrations**
   ```bash
   python -m alembic upgrade head
   ```

6. **Test the setup**
   ```bash
   python test_backend.py
   ```

7. **Start the server**
   ```bash
   uvicorn app.main:app --reload --host 0.0.0.0 --port 8000
   ```

## File Storage

Files are stored in the local filesystem under `uploads/cases/{case_id}/` with the following structure:

```
uploads/
└── cases/
    ├── case-id-1/
    │   ├── file-uuid-1.pdf
    │   └── file-uuid-2.pdf
    └── case-id-2/
        └── file-uuid-3.pdf
```

## Data Models

### Case Model
```python
{
  "id": "uuid",
  "internal_case_number": "string (optional)",
  "case_name": "string (required)",
  "date_of_loss": "date (optional)",
  "date_of_lawsuit": "date (optional)",
  "date_of_noc": "date (optional)",
  "date_received_from_carrier": "date (required)",
  "collection_id": "uuid (optional)",
  "user_id": "uuid (required)",
  "created_at": "datetime",
  "updated_at": "datetime"
}
```

### Collection Model
```python
{
  "id": "uuid",
  "name": "string (required)",
  "description": "string (optional)",
  "is_expanded": "boolean",
  "parent_id": "uuid (optional)",
  "user_id": "uuid (required)",
  "created_at": "datetime",
  "updated_at": "datetime"
}
```

### Case File Model
```python
{
  "id": "uuid",
  "filename": "string",
  "original_filename": "string",
  "file_size": "integer",
  "file_type": "string",
  "file_path": "string",
  "upload_status": "string",
  "error_message": "string (optional)",
  "case_id": "uuid",
  "user_id": "uuid",
  "created_at": "datetime",
  "updated_at": "datetime"
}
```

## Error Handling

The API returns appropriate HTTP status codes:

- `200` - Success
- `201` - Created
- `400` - Bad Request (validation errors)
- `401` - Unauthorized (invalid/missing token)
- `404` - Not Found
- `500` - Internal Server Error

Error responses include a `detail` field with the error message.

## Security

- JWT-based authentication
- User isolation (users can only access their own data)
- File type validation (PDF only)
- File size limits (50MB per file)
- Input validation and sanitization

## Development

### Running Tests
```bash
python test_backend.py
```

### API Documentation
Once the server is running, visit:
- Swagger UI: http://localhost:8000/docs
- ReDoc: http://localhost:8000/redoc

### Database Migrations
```bash
# Create new migration
alembic revision --autogenerate -m "Description"

# Apply migrations
alembic upgrade head

# Rollback migration
alembic downgrade -1
```

## Frontend Integration

The backend is designed to work with the React frontend. Key integration points:

1. **Authentication**: Use JWT tokens from `/auth/login`
2. **Tree Structure**: Use `/api/tree` for the sidebar navigation
3. **Case Creation**: Use `/api/cases` for form submission
4. **File Upload**: Use `/api/cases/{case_id}/files` for PDF uploads
5. **Drag & Drop**: Use `/api/cases/{case_id}/move` for moving cases between collections

## Production Deployment

For production deployment:

1. Use a production database (PostgreSQL)
2. Configure proper CORS settings
3. Use environment variables for sensitive data
4. Set up proper file storage (consider cloud storage)
5. Configure logging and monitoring
6. Use HTTPS
7. Set up proper backup strategies 