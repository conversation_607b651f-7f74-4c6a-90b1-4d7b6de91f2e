# See https://help.github.com/articles/ignoring-files/ for more about ignoring files.

# dependencies
/frontend/node_modules
/backend/venv
/frontend/.pnp
/frontend/.pnp.js
/frontend/.yarn/install-state.gz
/backend/venv
/backend/uploads

# testing
/coverage

# next.js
/frontend/.next/
/frontend/out/

# production
/frontend/build

# misc
.DS_Store
*.pem

# debug
/frontend/npm-debug.log*
/frontend/yarn-debug.log*
/frontend/yarn-error.log*

# local env files
/frontend/.env*.local
/backend/.env*.local
/backend/.env.development.local
/backend/.env.test.local
/backend/.env.production.local

# vercel
/frontend/.vercel   

# typescript
/frontend/*.tsbuildinfo
/frontend/next-env.d.ts
