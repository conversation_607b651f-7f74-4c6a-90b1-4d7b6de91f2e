'use client';

import { useState, useEffect } from 'react';
import DashboardLayout from '@/components/layouts/DashboardLayout';
import { Button } from '@/components/ui/button';
import { Card } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { SmartTable } from '@/components/ui/smart-table';
import { FadeIn, SlideIn } from '@/components/ui/loading';
import { DocumentViewer } from '@/components/ui/document-viewer';
import { cn } from '@/lib/utils';
import {
  Search,
  Download,
  Upload,
  RefreshCw,
  ChevronDown,
  ChevronRight,
  FileText,
  Folder,
  FolderOpen,
  ZoomIn,
  ZoomOut,
  RotateCw,
  Square,
  X,
  Flag,
  Plus,
  ChevronLeft,
  Info,
  User,
  FolderPlus,
  List
} from 'lucide-react';

interface Document {
  id: string;
  family: string;
  title: string;
  mark: string;
  issues: string;
  reviewer: string;
  size: string;
  type: 'log' | 'doc' | 'email' | 'policy' | 'medical' | 'claims' | 'bills' | 'lawsuit' | 'pdf' | 'image';
  status: 'reviewed' | 'pending' | 'flagged';
  uploadDate: string;
  pageCount?: number;
  reports?: string[];
}

interface DocumentTreeNode {
  id: string;
  name: string;
  type: 'folder' | 'file';
  children?: DocumentTreeNode[];
  expanded?: boolean;
  docType?: string;
  size?: string;
  status?: string;
}

export default function DocumentReviewPage() {
  const [selectedDocument, setSelectedDocument] = useState<Document | null>(null);
  const [currentPage, setCurrentPage] = useState(1);
  const [zoomLevel, setZoomLevel] = useState(100);
  const [selectedTool, setSelectedTool] = useState<'select' | 'highlight' | 'redact'>('select');
  const [selectedColor, setSelectedColor] = useState('#ffff00');
  const [sidebarCollapsed, setSidebarCollapsed] = useState(false);
  const [rightSidebarCollapsed, setRightSidebarCollapsed] = useState(false);
  const [documentListCollapsed, setDocumentListCollapsed] = useState(false);
  const [searchTerm, setSearchTerm] = useState('');
  const [loading, setLoading] = useState(true);
  const [currentReportId, setCurrentReportId] = useState<string>('');
  const [filteredDocuments, setFilteredDocuments] = useState<Document[]>([]);

  // Filter states
  const [activeFilters, setActiveFilters] = useState<{
    documentTypes: Set<string>;
    reviewers: Set<string>;
    statuses: Set<string>;
    dateRange: { start: string; end: string } | null;
  }>({
    documentTypes: new Set(),
    reviewers: new Set(),
    statuses: new Set(),
    dateRange: null
  });

  // Case management state
  const [caseInfo] = useState({
    relatedCases: 3,
    activeCases: 12,
    recentCaseActivity: 'Case #IC-2024-001 updated 2 hours ago'
  });

  // Function to get report name by ID and document type
  const getReportName = (documentType: string, reportId: string): string => {
    const reportMappings: Record<string, Record<string, string>> = {
      medical: {
        'medical-summary': 'Medical Summary Report',
        'medical-diagnostic': 'Diagnostic Report'
      },
      claims: {
        'claims-summary': 'Insurance Claim Summary'
      },
      bills: {
        'billing-summary': 'Medical Billing Summary'
      },
      lawsuit: {
        'lawsuit-summary': 'Legal Case Summary'
      },
      log: {
        'log-summary': 'Claim File Activity Log'
      },
      email: {
        'email-summary': 'Email Communication Summary'
      },
      policy: {
        'policy-summary': 'Insurance Policy Summary'
      },
      doc: {
        'memo-summary': 'Internal Document Summary'
      },
      pdf: {
        'structural-report': 'Structural Analysis Report'
      }
    };

    return reportMappings[documentType]?.[reportId] || 'Document Report';
  };

  // Function to generate breadcrumb based on selected document and report
  const generateBreadcrumb = () => {
    if (!selectedDocument) {
      return 'Matter: Insurance Claim Review > Select Document';
    }

    const documentTypeMap: Record<string, string> = {
      log: 'Claim File Log',
      doc: 'Insurance Company docs',
      email: 'Email',
      policy: 'Policy',
      medical: 'Medical Records',
      claims: 'Claims',
      bills: 'Medical Bills',
      lawsuit: 'Lawsuit Docs',
      pdf: 'Insurance Company docs', // Default PDF to Insurance Company docs
      image: 'Insurance Company docs'
    };

    const documentCategory = documentTypeMap[selectedDocument.type] || 'Insurance Company docs';
    const reportName = currentReportId ? getReportName(selectedDocument.type, currentReportId) : 'Document View';
    const pageInfo = selectedDocument.pageCount && selectedDocument.pageCount > 1 ? ` (Page ${currentPage}/${selectedDocument.pageCount})` : '';

    return `Matter: Insurance Claim Review > ${documentCategory} > ${reportName}${pageInfo}`;
  };

  // Custom document selection handler
  const handleDocumentSelect = (document: Document) => {
    setSelectedDocument(document);
    setCurrentReportId(''); // Reset report selection when changing documents
  };

  // Combined search and filter function
  const filterDocuments = (docs: Document[], searchTerm: string, filters: typeof activeFilters): Document[] => {
    let filtered = docs;

    // Apply search filter
    if (searchTerm.trim()) {
      const term = searchTerm.toLowerCase();
      filtered = filtered.filter(doc => {
        // Search in multiple fields
        const searchableFields = [
          doc.title,
          doc.reviewer,
          doc.family,
          doc.mark,
          doc.issues,
          doc.type,
          doc.status,
          doc.size,
          doc.id,
          ...(doc.reports || []) // Also search in report types
        ];

        return searchableFields.some(field =>
          field && field.toString().toLowerCase().includes(term)
        );
      });
    }

    // Apply document type filters
    if (filters.documentTypes.size > 0) {
      filtered = filtered.filter(doc => filters.documentTypes.has(doc.type));
    }

    // Apply reviewer filters
    if (filters.reviewers.size > 0) {
      filtered = filtered.filter(doc => filters.reviewers.has(doc.reviewer));
    }

    // Apply status filters
    if (filters.statuses.size > 0) {
      filtered = filtered.filter(doc => filters.statuses.has(doc.status));
    }

    // Apply date range filter
    if (filters.dateRange) {
      const { start, end } = filters.dateRange;
      if (start || end) {
        filtered = filtered.filter(doc => {
          const docDate = new Date(doc.uploadDate);
          const startDate = start ? new Date(start) : null;
          const endDate = end ? new Date(end) : null;

          if (startDate && docDate < startDate) return false;
          if (endDate && docDate > endDate) return false;
          return true;
        });
      }
    }

    return filtered;
  };

  // Handle search input changes
  const handleSearchChange = (value: string) => {
    setSearchTerm(value);
    // Filters will be applied automatically in useEffect
  };

  // Handle refresh search and filters
  const handleRefreshSearch = () => {
    setSearchTerm('');
    setActiveFilters({
      documentTypes: new Set(),
      reviewers: new Set(),
      statuses: new Set(),
      dateRange: null
    });
    // Filters will be applied automatically in useEffect
  };

  // Filter toggle functions
  const toggleDocumentTypeFilter = (docType: string) => {
    setActiveFilters(prev => {
      const newTypes = new Set(prev.documentTypes);
      if (newTypes.has(docType)) {
        newTypes.delete(docType);
      } else {
        newTypes.add(docType);
      }
      return { ...prev, documentTypes: newTypes };
    });
  };

  const toggleReviewerFilter = (reviewer: string) => {
    setActiveFilters(prev => {
      const newReviewers = new Set(prev.reviewers);
      if (newReviewers.has(reviewer)) {
        newReviewers.delete(reviewer);
      } else {
        newReviewers.add(reviewer);
      }
      return { ...prev, reviewers: newReviewers };
    });
  };

  const toggleStatusFilter = (status: string) => {
    setActiveFilters(prev => {
      const newStatuses = new Set(prev.statuses);
      if (newStatuses.has(status)) {
        newStatuses.delete(status);
      } else {
        newStatuses.add(status);
      }
      return { ...prev, statuses: newStatuses };
    });
  };

  const setDateRangeFilter = (start: string, end: string) => {
    setActiveFilters(prev => ({
      ...prev,
      dateRange: { start, end }
    }));
  };

  // Case management handlers
  const handleCreateCase = () => {
    // Navigate to create case page
    window.location.href = '/create-case';
  };

  const handleAllCases = () => {
    // Navigate to all cases page (to be created)
    alert('All Cases page will be created next. For now, this shows the create case functionality.');
  };

  // Function to highlight search terms in text
  const highlightSearchTerm = (text: string, searchTerm: string) => {
    if (!searchTerm.trim()) return text;

    const regex = new RegExp(`(${searchTerm.replace(/[.*+?^${}()|[\]\\]/g, '\\$&')})`, 'gi');
    const parts = text.split(regex);

    return parts.map((part, index) =>
      regex.test(part) ? (
        <span key={index} className="bg-yellow-200 font-medium">
          {part}
        </span>
      ) : part
    );
  };

  // Mock data for documents
  const [documents] = useState<Document[]>([
    {
      id: '3776',
      family: '3776',
      title: 'Medical Assessment Report - Patient Smith',
      mark: '',
      issues: '',
      reviewer: 'Dr. John Doe',
      size: '2.3 MB',
      type: 'medical',
      status: 'reviewed',
      uploadDate: '2024-01-15',
      pageCount: 8,
      reports: ['medical-summary', 'medical-diagnostic']
    },
    {
      id: '3779',
      family: '3779',
      title: 'Insurance Claim Documentation',
      mark: '',
      issues: '',
      reviewer: 'Mike Johnson',
      size: '1.2 MB',
      type: 'claims',
      status: 'pending',
      uploadDate: '2024-01-16',
      pageCount: 15,
      reports: ['claims-summary', 'claims-investigation']
    },
    {
      id: '3781',
      family: '3781',
      title: 'Medical Billing Statement',
      mark: 'Privileged',
      issues: '',
      reviewer: 'Sarah Wilson',
      size: '656 kB',
      type: 'bills',
      status: 'reviewed',
      uploadDate: '2024-01-17',
      pageCount: 5,
      reports: ['billing-summary', 'billing-detailed']
    },
    {
      id: '3782',
      family: '3782',
      title: 'Claim File Activity Log',
      mark: '',
      issues: '',
      reviewer: 'Sarah Wilson',
      size: '45 kB',
      type: 'log',
      status: 'flagged',
      uploadDate: '2024-01-18',
      pageCount: 12,
      reports: ['log-summary']
    },
    {
      id: '3783',
      family: '3783',
      title: 'Legal Correspondence - Lawsuit Filing',
      mark: 'Confidential',
      issues: '',
      reviewer: 'Legal Team',
      size: '890 kB',
      type: 'lawsuit',
      status: 'pending',
      uploadDate: '2024-01-19',
      pageCount: 25,
      reports: ['lawsuit-summary']
    },
    {
      id: '3784',
      family: '3784',
      title: 'Email Thread - Adjuster Communications',
      mark: '',
      issues: '',
      reviewer: 'Claims Team',
      size: '234 kB',
      type: 'email',
      status: 'reviewed',
      uploadDate: '2024-01-20',
      pageCount: 3,
      reports: ['email-summary']
    },
    {
      id: '3785',
      family: '3785',
      title: 'Auto Insurance Policy Document',
      mark: '',
      issues: '',
      reviewer: 'Policy Team',
      size: '1.8 MB',
      type: 'policy',
      status: 'reviewed',
      uploadDate: '2024-01-21',
      pageCount: 45,
      reports: ['policy-summary']
    },
    {
      id: '3786',
      family: '3786',
      title: 'Insurance Company Internal Memo',
      mark: 'Confidential',
      issues: '',
      reviewer: 'Internal Review',
      size: '567 kB',
      type: 'doc',
      status: 'pending',
      uploadDate: '2024-01-22',
      pageCount: 8,
      reports: ['memo-summary']
    }
  ]);

  // Mock document tree data
  const [documentTree] = useState<DocumentTreeNode[]>([
    {
      id: 'review',
      name: 'Review',
      type: 'folder',
      expanded: true,
      children: [
        {
          id: 'ggo-llc',
          name: 'GGO LLC Sample Matter 2 (Review)',
          type: 'folder',
          expanded: true,
          children: [
            {
              id: 'docs',
              name: 'Docs',
              type: 'folder',
              expanded: true,
              children: [
                {
                  id: 'binders',
                  name: 'Binders',
                  type: 'folder',
                  expanded: false
                },
                {
                  id: 'collections',
                  name: 'Collections',
                  type: 'folder',
                  expanded: true,
                  children: [
                    {
                      id: 'jeremy-email',
                      name: "Jeremy's Email (1112)",
                      type: 'folder',
                      expanded: false
                    }
                  ]
                },
                {
                  id: 'marks',
                  name: 'Marks',
                  type: 'folder',
                  expanded: false
                },
                {
                  id: 'issues',
                  name: 'Issues',
                  type: 'folder',
                  expanded: false
                },
                {
                  id: 'more-filters',
                  name: 'More Filters',
                  type: 'folder',
                  expanded: true,
                  children: [
                    {
                      id: 'doc-types',
                      name: 'Doc Types',
                      type: 'folder',
                      expanded: true,
                      children: [
                        { id: 'claim-file-log', name: 'Claim File Log', type: 'file', docType: 'log' },
                        { id: 'insurance-company-docs', name: 'Insurance Company docs', type: 'file', docType: 'doc' },
                        { id: 'email', name: 'Email', type: 'file', docType: 'email' },
                        { id: 'policy', name: 'Policy', type: 'file', docType: 'policy' },
                        { id: 'medical-records', name: 'Medical Records', type: 'file', docType: 'medical' },
                        { id: 'claims', name: 'Claims', type: 'file', docType: 'claims' },
                        { id: 'medical-bills', name: 'Medical Bills', type: 'file', docType: 'bills' },
                        { id: 'lawsuit-docs', name: 'Lawsuit Docs', type: 'file', docType: 'lawsuit' }
                      ]
                    },
                    {
                      id: 'reviewers',
                      name: 'Reviewers',
                      type: 'folder',
                      expanded: true,
                      children: [
                        { id: 'dr-john-doe', name: 'Dr. John Doe', type: 'file', docType: 'reviewer' },
                        { id: 'mike-johnson', name: 'Mike Johnson', type: 'file', docType: 'reviewer' },
                        { id: 'sarah-wilson', name: 'Sarah Wilson', type: 'file', docType: 'reviewer' },
                        { id: 'claims-team', name: 'Claims Team', type: 'file', docType: 'reviewer' },
                        { id: 'policy-team', name: 'Policy Team', type: 'file', docType: 'reviewer' },
                        { id: 'legal-team', name: 'Legal Team', type: 'file', docType: 'reviewer' },
                        { id: 'internal-review', name: 'Internal Review', type: 'file', docType: 'reviewer' }
                      ]
                    },
                    {
                      id: 'status',
                      name: 'Status',
                      type: 'folder',
                      expanded: true,
                      children: [
                        { id: 'reviewed', name: 'Reviewed', type: 'file', docType: 'status' },
                        { id: 'pending', name: 'Pending', type: 'file', docType: 'status' },
                        { id: 'flagged', name: 'Flagged', type: 'file', docType: 'status' }
                      ]
                    }
                  ]
                }
              ]
            }
          ]
        }
      ]
    }
  ]);

  useEffect(() => {
    // Initialize filtered documents
    setFilteredDocuments(documents);
  }, [documents]);

  // Apply filters whenever search term or filters change
  useEffect(() => {
    const filtered = filterDocuments(documents, searchTerm, activeFilters);
    setFilteredDocuments(filtered);

    // If current selected document is not in filtered results, clear selection
    if (selectedDocument && !filtered.find(doc => doc.id === selectedDocument.id)) {
      setSelectedDocument(null);
      setCurrentReportId('');
    }
  }, [searchTerm, activeFilters, documents, selectedDocument]);

  useEffect(() => {
    // Simulate loading
    const timer = setTimeout(() => {
      setLoading(false);
      if (documents.length > 0) {
        handleDocumentSelect(documents[0]);
      }
    }, 1000);

    return () => clearTimeout(timer);
  }, [documents]);

  // Add keyboard shortcuts
  useEffect(() => {
    const handleKeyDown = (event: KeyboardEvent) => {
      // Search shortcut (Ctrl/Cmd + F)
      if ((event.ctrlKey || event.metaKey) && event.key === 'f') {
        event.preventDefault();
        const searchInput = document.querySelector('input[placeholder="Search documents..."]') as HTMLInputElement;
        if (searchInput) {
          searchInput.focus();
          searchInput.select();
        }
      }

      // Create case shortcut (Ctrl/Cmd + Shift + C)
      if ((event.ctrlKey || event.metaKey) && event.shiftKey && event.key === 'C') {
        event.preventDefault();
        handleCreateCase();
      }

      // All cases shortcut (Ctrl/Cmd + Shift + A)
      if ((event.ctrlKey || event.metaKey) && event.shiftKey && event.key === 'A') {
        event.preventDefault();
        handleAllCases();
      }

      // Toggle document list shortcut (Ctrl/Cmd + D)
      if ((event.ctrlKey || event.metaKey) && event.key === 'd') {
        event.preventDefault();
        setDocumentListCollapsed(!documentListCollapsed);
      }
    };

    document.addEventListener('keydown', handleKeyDown);
    return () => document.removeEventListener('keydown', handleKeyDown);
  }, []);

  const colors = [
    '#ffff00', // Yellow
    '#00ff00', // Green
    '#ff0000', // Red
    '#0000ff', // Blue
    '#ff00ff', // Magenta
    '#00ffff', // Cyan
    '#ffa500', // Orange
    '#800080'  // Purple
  ];

  return (
    <div className="h-screen flex flex-col bg-gray-50">
      {/* Top Navigation Bar - Dynamic Breadcrumb */}
      <div className="bg-red-50 border-b border-red-200 px-4 py-2 flex-shrink-0">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-2 overflow-x-auto">
            <div className="bg-red-600 text-white px-3 py-1 rounded text-sm font-medium flex items-center">
              <FileText className="h-4 w-4 mr-2" />
              {generateBreadcrumb()}
            </div>
            {selectedDocument && (
              <div className="text-xs text-gray-500 bg-gray-100 px-2 py-1 rounded">
                {selectedDocument.title}
              </div>
            )}
          </div>
          <div className="flex items-center space-x-3 text-sm text-gray-600 font-medium hidden md:block">
            <span>Insurance Claim Review</span>
            <div className="flex items-center space-x-1 bg-green-100 text-green-700 px-2 py-1 rounded-full text-xs">
              <div className="w-2 h-2 bg-green-500 rounded-full"></div>
              <span>{caseInfo.activeCases} Active Cases</span>
            </div>
          </div>
        </div>
      </div>

      {/* Toolbar */}
      <div className="bg-white border-b border-gray-200 px-4 py-2 flex-shrink-0">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-2 overflow-x-auto">
            <Button
              variant="outline"
              size="sm"
              className="whitespace-nowrap"
              onClick={handleRefreshSearch}
            >
              <RefreshCw className="h-4 w-4 mr-1" />
              Refresh Search
            </Button>
            <Button variant="outline" size="sm" className="whitespace-nowrap">
              <Download className="h-4 w-4 mr-1" />
              Export Grid
            </Button>
            <Button variant="outline" size="sm" className="whitespace-nowrap">
              <Upload className="h-4 w-4 mr-1" />
              Export Documents
            </Button>

            {/* Separator */}
            <div className="h-6 w-px bg-gray-300 mx-2"></div>

            {/* Case Management Buttons */}
            <Button
              variant="default"
              size="sm"
              className="whitespace-nowrap bg-blue-600 hover:bg-blue-700 text-white relative"
              onClick={handleCreateCase}
              title="Create a new case from current documents (Ctrl+Shift+C)"
            >
              <Plus className="h-4 w-4 mr-1" />
              Create Case
              {filteredDocuments.length > 0 && (
                <span className="ml-2 bg-blue-500 text-white text-xs px-1.5 py-0.5 rounded-full">
                  {filteredDocuments.length}
                </span>
              )}
            </Button>

            <Button
              variant="outline"
              size="sm"
              className="whitespace-nowrap border-blue-600 text-blue-600 hover:bg-blue-50"
              onClick={handleAllCases}
              title="View all cases in the system (Ctrl+Shift+A)"
            >
              <List className="h-4 w-4 mr-1" />
              All Cases
            </Button>

            {/* Search Results Summary */}
            {searchTerm && (
              <div className="flex items-center space-x-2 text-sm text-gray-600 bg-blue-50 px-3 py-1 rounded">
                <Search className="h-4 w-4 text-blue-600" />
                <span>
                  Found {filteredDocuments.length} result{filteredDocuments.length !== 1 ? 's' : ''} for "{searchTerm}"
                </span>
                <button
                  onClick={() => handleSearchChange('')}
                  className="text-blue-600 hover:text-blue-800 ml-2"
                >
                  <X className="h-3 w-3" />
                </button>
              </div>
            )}

            {/* Active Filters Summary */}
            {(activeFilters.documentTypes.size > 0 || activeFilters.reviewers.size > 0 || activeFilters.statuses.size > 0) && (
              <div className="flex items-center space-x-2 text-sm text-gray-600 bg-green-50 px-3 py-1 rounded">
                <span className="text-green-700 font-medium">Active Filters:</span>

                {/* Document Type Filters */}
                {Array.from(activeFilters.documentTypes).map(type => (
                  <span key={type} className="inline-flex items-center bg-green-100 text-green-800 px-2 py-1 rounded text-xs">
                    {type}
                    <button
                      onClick={() => toggleDocumentTypeFilter(type)}
                      className="ml-1 text-green-600 hover:text-green-800"
                    >
                      <X className="h-3 w-3" />
                    </button>
                  </span>
                ))}

                {/* Reviewer Filters */}
                {Array.from(activeFilters.reviewers).map(reviewer => (
                  <span key={reviewer} className="inline-flex items-center bg-blue-100 text-blue-800 px-2 py-1 rounded text-xs">
                    {reviewer}
                    <button
                      onClick={() => toggleReviewerFilter(reviewer)}
                      className="ml-1 text-blue-600 hover:text-blue-800"
                    >
                      <X className="h-3 w-3" />
                    </button>
                  </span>
                ))}

                {/* Status Filters */}
                {Array.from(activeFilters.statuses).map(status => (
                  <span key={status} className="inline-flex items-center bg-purple-100 text-purple-800 px-2 py-1 rounded text-xs">
                    {status}
                    <button
                      onClick={() => toggleStatusFilter(status)}
                      className="ml-1 text-purple-600 hover:text-purple-800"
                    >
                      <X className="h-3 w-3" />
                    </button>
                  </span>
                ))}

                {/* Clear All Filters */}
                <button
                  onClick={handleRefreshSearch}
                  className="text-gray-600 hover:text-gray-800 ml-2"
                  title="Clear all filters"
                >
                  Clear All
                </button>
              </div>
            )}
          </div>

          <div className="flex items-center space-x-4 text-sm text-gray-600 hidden lg:flex">
            <span className="whitespace-nowrap">Current Doc: {selectedDocument?.id || 'None'}</span>
            <span className="whitespace-nowrap">Pages: {selectedDocument?.pageCount || 0}</span>
            <span className="whitespace-nowrap">
              Filters: {activeFilters.documentTypes.size + activeFilters.reviewers.size + activeFilters.statuses.size + (activeFilters.dateRange ? 1 : 0)} active
            </span>
            <span className="whitespace-nowrap text-blue-600 font-medium" title={caseInfo.recentCaseActivity}>
              Cases: {caseInfo.relatedCases} related
            </span>
          </div>
        </div>
      </div>

      {/* Main Content Area */}
      <div className="flex-1 flex overflow-hidden">
        {/* Left Sidebar - Document Tree */}
        <div className={cn(
          "bg-white border-r border-gray-200 transition-all duration-300 flex-shrink-0",
          sidebarCollapsed ? "w-12" : "w-80 lg:w-80 md:w-64 sm:w-56"
        )}>
          {!sidebarCollapsed && (
            <div className="h-full flex flex-col">
              {/* Sidebar Header */}
              <div className="p-4 border-b border-gray-200 flex-shrink-0 bg-gray-50">
                <div className="flex items-center justify-between mb-3">
                  <h3 className="text-sm font-semibold text-gray-900 truncate">Review</h3>
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => setSidebarCollapsed(true)}
                    className="flex-shrink-0 hover:bg-gray-200"
                  >
                    <ChevronLeft className="h-4 w-4" />
                  </Button>
                </div>

                {/* Search */}
                <div className="relative">
                  <Search className="absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 text-gray-400" />
                  <Input
                    placeholder="Search documents..."
                    value={searchTerm}
                    onChange={(e) => handleSearchChange(e.target.value)}
                    className="pl-10 pr-8 h-8 text-sm bg-white border-gray-300 focus:border-blue-500 focus:ring-1 focus:ring-blue-500"
                  />
                  {searchTerm && (
                    <button
                      onClick={() => handleSearchChange('')}
                      className="absolute right-2 top-1/2 h-4 w-4 -translate-y-1/2 text-gray-400 hover:text-gray-600"
                    >
                      <X className="h-4 w-4" />
                    </button>
                  )}
                </div>
              </div>

              {/* Document Tree */}
              <div className="flex-1 overflow-y-auto p-2">
                <DocumentTreeComponent
                  nodes={documentTree}
                  onNodeSelect={(node) => console.log('Selected:', node)}
                  activeFilters={activeFilters}
                  onToggleDocumentType={toggleDocumentTypeFilter}
                  onToggleReviewer={toggleReviewerFilter}
                  onToggleStatus={toggleStatusFilter}
                  onSetDateRange={setDateRangeFilter}
                />
              </div>

              {/* Date Range Filter */}
              <div className="p-4 border-t border-gray-200 flex-shrink-0 bg-gray-50">
                <div className="space-y-3">
                  <h4 className="text-sm font-semibold text-gray-900 flex items-center">
                    <ChevronDown className="h-3 w-3 mr-1" />
                    Date Range
                  </h4>
                  <select className="w-full text-sm border border-gray-300 rounded-md px-3 py-2 bg-white focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors">
                    <option value="all">All Time</option>
                    <option value="today">Today</option>
                    <option value="week">This Week</option>
                    <option value="month">This Month</option>
                    <option value="quarter">This Quarter</option>
                    <option value="year">This Year</option>
                    <option value="custom">Custom Range</option>
                  </select>
                </div>
              </div>
            </div>
          )}

          {sidebarCollapsed && (
            <div className="p-2">
              <Button
                variant="ghost"
                size="sm"
                onClick={() => setSidebarCollapsed(false)}
                className="w-full"
              >
                <ChevronRight className="h-4 w-4" />
              </Button>
            </div>
          )}
        </div>

        {/* Main Document Viewer */}
        <div className="flex-1 flex flex-col bg-white">
          {/* Document Viewer Toolbar */}
          <div className="border-b border-gray-200 p-3">
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-2">
                <span className="text-sm font-medium">
                  Document Browser: 3791 - Noiret.pdf
                </span>
                <Button variant="ghost" size="sm">
                  <X className="h-4 w-4" />
                </Button>
              </div>

              <div className="flex items-center space-x-2">
                <Button variant="outline" size="sm">
                  <Flag className="h-4 w-4 mr-1" />
                  Clear All Highlights
                </Button>
                <Button variant="outline" size="sm">
                  <Square className="h-4 w-4 mr-1" />
                  Clear All Redactions
                </Button>
              </div>
            </div>
          </div>

          {/* Document Tools */}
          <div className="border-b border-gray-200 p-3 flex-shrink-0">
            <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between space-y-3 lg:space-y-0">
              <div className="flex flex-col sm:flex-row sm:items-center space-y-2 sm:space-y-0 sm:space-x-4">
                <div className="flex items-center space-x-1 overflow-x-auto">
                  <span className="text-sm text-gray-600 whitespace-nowrap">Tools:</span>
                  <Button
                    variant={selectedTool === 'select' ? 'default' : 'outline'}
                    size="sm"
                    onClick={() => setSelectedTool('select')}
                    className="whitespace-nowrap"
                  >
                    Select
                  </Button>
                  <Button
                    variant={selectedTool === 'highlight' ? 'default' : 'outline'}
                    size="sm"
                    onClick={() => setSelectedTool('highlight')}
                    className="whitespace-nowrap"
                  >
                    Highlight
                  </Button>
                  <Button
                    variant={selectedTool === 'redact' ? 'default' : 'outline'}
                    size="sm"
                    onClick={() => setSelectedTool('redact')}
                    className="whitespace-nowrap"
                  >
                    Redact
                  </Button>
                </div>

                <div className="flex items-center space-x-1">
                  <span className="text-sm text-gray-600 whitespace-nowrap">Color:</span>
                  <div className="flex space-x-1 overflow-x-auto">
                    {colors.map((color) => (
                      <button
                        key={color}
                        className={cn(
                          "w-6 h-6 rounded border-2 transition-all flex-shrink-0",
                          selectedColor === color ? "border-gray-800 scale-110" : "border-gray-300"
                        )}
                        style={{ backgroundColor: color }}
                        onClick={() => setSelectedColor(color)}
                      />
                    ))}
                  </div>
                </div>
              </div>

              <div className="flex items-center justify-between sm:justify-end space-x-2">
                {/* Mobile Document Info Button */}
                <Button
                  variant="outline"
                  size="sm"
                  className="lg:hidden"
                  onClick={() => setRightSidebarCollapsed(!rightSidebarCollapsed)}
                >
                  <Info className="h-4 w-4" />
                </Button>

                <Button variant="outline" size="sm" onClick={() => setZoomLevel(Math.max(25, zoomLevel - 25))}>
                  <ZoomOut className="h-4 w-4" />
                </Button>
                <span className="text-sm text-gray-600 min-w-[60px] text-center">
                  {zoomLevel}%
                </span>
                <Button variant="outline" size="sm" onClick={() => setZoomLevel(Math.min(200, zoomLevel + 25))}>
                  <ZoomIn className="h-4 w-4" />
                </Button>
                <Button variant="outline" size="sm">
                  <RotateCw className="h-4 w-4" />
                </Button>
              </div>
            </div>
          </div>

          {/* Document Content */}
          <div className="flex-1 overflow-auto bg-gray-100 p-4">
            <div className="max-w-4xl mx-auto">
              {selectedDocument ? (
                <DocumentViewer
                  document={selectedDocument}
                  currentPage={currentPage}
                  zoomLevel={zoomLevel}
                  selectedTool={selectedTool}
                  selectedColor={selectedColor}
                  onReportChange={(reportId) => {
                    console.log('Report changed to:', reportId);
                    setCurrentReportId(reportId);
                  }}
                />
              ) : (
                <div className="flex items-center justify-center h-full">
                  <div className="text-center text-gray-500">
                    <FileText className="h-16 w-16 mx-auto mb-4 opacity-50" />
                    <p>Select a document to view</p>
                  </div>
                </div>
              )}
            </div>
          </div>

          {/* Page Navigation */}
          {selectedDocument && (
            <div className="border-t border-gray-200 p-3">
              <div className="flex items-center justify-center space-x-4">
                <Button
                  variant="outline"
                  size="sm"
                  disabled={currentPage <= 1}
                  onClick={() => setCurrentPage(currentPage - 1)}
                >
                  <ChevronLeft className="h-4 w-4" />
                </Button>
                <span className="text-sm text-gray-600">
                  Page {currentPage} of {selectedDocument.pageCount || 1}
                </span>
                <Button
                  variant="outline"
                  size="sm"
                  disabled={currentPage >= (selectedDocument.pageCount || 1)}
                  onClick={() => setCurrentPage(currentPage + 1)}
                >
                  <ChevronRight className="h-4 w-4" />
                </Button>
              </div>
            </div>
          )}
        </div>

        {/* Right Sidebar - Document Information */}
        <div className={cn(
          "bg-white border-l border-gray-200 transition-all duration-300 flex-shrink-0 hidden lg:block",
          rightSidebarCollapsed ? "w-12" : "w-80"
        )}>
          {!rightSidebarCollapsed && (
            <div className="h-full flex flex-col">
              {/* Sidebar Header */}
              <div className="p-4 border-b border-gray-200 flex-shrink-0">
                <div className="flex items-center justify-between mb-3">
                  <h3 className="text-sm font-medium text-gray-900 truncate">Document Information</h3>
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => setRightSidebarCollapsed(true)}
                    className="flex-shrink-0"
                  >
                    <ChevronRight className="h-4 w-4" />
                  </Button>
                </div>
              </div>

              {/* Document Details */}
              <div className="flex-1 overflow-y-auto">
                {selectedDocument && (
                  <DocumentInfoPanel document={selectedDocument} />
                )}
              </div>
            </div>
          )}

          {rightSidebarCollapsed && (
            <div className="p-2">
              <Button
                variant="ghost"
                size="sm"
                onClick={() => setRightSidebarCollapsed(false)}
                className="w-full"
              >
                <ChevronLeft className="h-4 w-4" />
              </Button>
            </div>
          )}
        </div>
      </div>

      {/* Mobile Document Info Overlay */}
      {!rightSidebarCollapsed && (
        <div className="lg:hidden fixed inset-0 z-50 bg-black bg-opacity-50" onClick={() => setRightSidebarCollapsed(true)}>
          <div className="absolute right-0 top-0 h-full w-80 bg-white shadow-xl" onClick={(e) => e.stopPropagation()}>
            <div className="h-full flex flex-col">
              {/* Sidebar Header */}
              <div className="p-4 border-b border-gray-200 flex-shrink-0">
                <div className="flex items-center justify-between mb-3">
                  <h3 className="text-sm font-medium text-gray-900">Document Information</h3>
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => setRightSidebarCollapsed(true)}
                  >
                    <X className="h-4 w-4" />
                  </Button>
                </div>
              </div>

              {/* Document Details */}
              <div className="flex-1 overflow-y-auto">
                {selectedDocument && (
                  <DocumentInfoPanel document={selectedDocument} />
                )}
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Bottom Document List Table */}
      <div className={cn(
        "bg-white border-t border-gray-200 flex-shrink-0 transition-all duration-300 overflow-hidden",
        documentListCollapsed ? "h-12" : "h-56 max-h-56"
      )}>
        {/* Document List Header with Collapse Button */}
        <div className="flex items-center justify-between px-4 py-2 border-b border-gray-100 bg-gray-50">
          <h3 className="text-sm font-medium text-gray-900">
            Documents ({filteredDocuments.length})
          </h3>
          <button
            onClick={() => setDocumentListCollapsed(!documentListCollapsed)}
            className="p-1 hover:bg-gray-200 rounded transition-colors"
            title={documentListCollapsed ? "Expand document list (Ctrl+D)" : "Collapse document list (Ctrl+D)"}
          >
            {!documentListCollapsed ? (
              <ChevronDown className="h-4 w-4 text-gray-600" />
            ) : (
              <ChevronDown className="h-4 w-4 text-gray-600 rotate-180" />
            )}
          </button>
        </div>

        {!documentListCollapsed && (
          <div className="p-3 h-full overflow-y-auto">
            <DocumentListTable
              documents={filteredDocuments}
              onDocumentSelect={handleDocumentSelect}
              selectedDocument={selectedDocument}
              totalDocuments={documents.length}
              searchTerm={searchTerm}
              highlightSearchTerm={highlightSearchTerm}
            />
          </div>
        )}
      </div>
    </div>
  );
}

// Document Tree Component
function DocumentTreeComponent({
  nodes,
  onNodeSelect,
  activeFilters,
  onToggleDocumentType,
  onToggleReviewer,
  onToggleStatus,
  onSetDateRange
}: {
  nodes: DocumentTreeNode[];
  onNodeSelect: (node: DocumentTreeNode) => void;
  activeFilters: {
    documentTypes: Set<string>;
    reviewers: Set<string>;
    statuses: Set<string>;
    dateRange: { start: string; end: string } | null;
  };
  onToggleDocumentType: (docType: string) => void;
  onToggleReviewer: (reviewer: string) => void;
  onToggleStatus: (status: string) => void;
  onSetDateRange: (start: string, end: string) => void;
}) {
  const [expandedNodes, setExpandedNodes] = useState<Set<string>>(
    new Set(['review', 'ggo-llc', 'docs', 'collections', 'more-filters', 'doc-types', 'reviewers', 'status'])
  );

  const toggleNode = (nodeId: string) => {
    const newExpanded = new Set(expandedNodes);
    if (newExpanded.has(nodeId)) {
      newExpanded.delete(nodeId);
    } else {
      newExpanded.add(nodeId);
    }
    setExpandedNodes(newExpanded);
  };

  const toggleCheck = (nodeId: string, event: React.MouseEvent) => {
    event.stopPropagation();

    // Apply filters based on node type and ID
    const node = findNodeById(nodes, nodeId);

    if (node && node.docType) {
      // Document type filter
      if (['log', 'doc', 'email', 'policy', 'medical', 'claims', 'bills', 'lawsuit'].includes(node.docType)) {
        onToggleDocumentType(node.docType);
      }
      // Reviewer filter
      else if (node.docType === 'reviewer') {
        onToggleReviewer(node.name);
      }
      // Status filter
      else if (node.docType === 'status') {
        onToggleStatus(nodeId);
      }
    }
  };

  // Helper function to find node by ID
  const findNodeById = (nodes: DocumentTreeNode[], id: string): DocumentTreeNode | null => {
    for (const node of nodes) {
      if (node.id === id) return node;
      if (node.children) {
        const found = findNodeById(node.children, id);
        if (found) return found;
      }
    }
    return null;
  };

  const getNodeIcon = (node: DocumentTreeNode, isExpanded: boolean) => {
    if (node.type === 'folder') {
      return isExpanded ? (
        <FolderOpen className="h-4 w-4 mr-2 text-blue-500" />
      ) : (
        <Folder className="h-4 w-4 mr-2 text-blue-500" />
      );
    }

    // Different icons based on document type
    switch (node.docType) {
      case 'log':
        return <FileText className="h-4 w-4 mr-2 text-orange-500" />;
      case 'email':
        return <FileText className="h-4 w-4 mr-2 text-green-500" />;
      case 'policy':
        return <FileText className="h-4 w-4 mr-2 text-purple-500" />;
      case 'medical':
        return <FileText className="h-4 w-4 mr-2 text-red-500" />;
      case 'claims':
        return <FileText className="h-4 w-4 mr-2 text-yellow-600" />;
      case 'bills':
        return <FileText className="h-4 w-4 mr-2 text-pink-500" />;
      case 'lawsuit':
        return <FileText className="h-4 w-4 mr-2 text-gray-600" />;
      case 'reviewer':
        return <User className="h-4 w-4 mr-2 text-blue-600" />;
      default:
        return <FileText className="h-4 w-4 mr-2 text-gray-500" />;
    }
  };

  const renderNode = (node: DocumentTreeNode, level: number = 0) => {
    const isExpanded = expandedNodes.has(node.id);
    const hasChildren = node.children && node.children.length > 0;

    // Determine if checkbox should be checked based on active filters
    let isChecked = false;
    if (node.docType) {
      if (['log', 'doc', 'email', 'policy', 'medical', 'claims', 'bills', 'lawsuit'].includes(node.docType)) {
        isChecked = activeFilters.documentTypes.has(node.docType);
      } else if (node.docType === 'reviewer') {
        isChecked = activeFilters.reviewers.has(node.name);
      } else if (node.docType === 'status') {
        isChecked = activeFilters.statuses.has(node.id);
      }
    }

    return (
      <div key={node.id}>
        <div
          className={cn(
            "flex items-center py-1 px-2 text-sm hover:bg-blue-50 cursor-pointer rounded-sm",
            "transition-colors duration-150",
            isChecked && "bg-blue-50 border-l-2 border-blue-500"
          )}
          style={{ paddingLeft: `${level * 12 + 4}px` }}
          onClick={() => {
            if (hasChildren) {
              toggleNode(node.id);
            }
            onNodeSelect(node);
          }}
        >
          {/* Checkbox for filter items */}
          {(node.docType || node.id === 'date-range') && (
            <input
              type="checkbox"
              checked={isChecked}
              onChange={(e) => {
                e.stopPropagation();
                toggleCheck(node.id, e as any);
              }}
              className="mr-2 h-3 w-3 text-blue-600 focus:ring-blue-500 border-gray-400 rounded-sm cursor-pointer"
            />
          )}

          {/* Expand/collapse button */}
          {hasChildren && (
            <button
              className="mr-1 p-0.5 hover:bg-gray-200 rounded"
              onClick={(e) => {
                e.stopPropagation();
                toggleNode(node.id);
              }}
            >
              {isExpanded ? (
                <ChevronDown className="h-3 w-3" />
              ) : (
                <ChevronRight className="h-3 w-3" />
              )}
            </button>
          )}

          {!hasChildren && !(node.docType || node.id === 'date-range') && <div className="w-4 mr-1" />}

          {/* Node icon */}
          {getNodeIcon(node, isExpanded)}

          {/* Node name */}
          <span className={cn(
            "text-gray-700 truncate",
            isChecked && "font-medium text-blue-700"
          )}>
            {node.name}
          </span>
        </div>

        {hasChildren && isExpanded && (
          <div>
            {node.children!.map(child => renderNode(child, level + 1))}
          </div>
        )}
      </div>
    );
  };

  return (
    <div className="space-y-1">
      {nodes.map(node => renderNode(node))}
    </div>
  );
}



// Document Info Panel Component
function DocumentInfoPanel({ document }: { document: Document }) {
  return (
    <div className="p-4 space-y-6">
      {/* Document Tab */}
      <div>
        <div className="flex border-b border-gray-200">
          <button className="px-4 py-2 text-sm font-medium text-blue-600 border-b-2 border-blue-600">
            Document
          </button>
          <button className="px-4 py-2 text-sm font-medium text-gray-500 hover:text-gray-700">
            Page
          </button>
        </div>
      </div>

      {/* Static Information */}
      <div>
        <h4 className="text-sm font-medium text-gray-900 mb-3">Static Information</h4>
        <div className="space-y-2 text-sm">
          <div className="flex justify-between">
            <span className="text-gray-600">Collection:</span>
            <span className="text-gray-900">Jeremy's Email</span>
          </div>
          <div className="flex justify-between">
            <span className="text-gray-600">Document Name:</span>
            <span className="text-gray-900">Noiret.pdf</span>
          </div>
          <div className="flex justify-between">
            <span className="text-gray-600">File Size:</span>
            <span className="text-gray-900">{document.size}</span>
          </div>
          <div className="flex justify-between">
            <span className="text-gray-600">Upload Date:</span>
            <span className="text-gray-900">{document.uploadDate}</span>
          </div>
        </div>
      </div>

      {/* AI Extracted Information */}
      <div>
        <h4 className="text-sm font-medium text-gray-900 mb-3">AI Extracted Information</h4>
        <div className="space-y-2 text-sm">
          <div className="flex justify-between">
            <span className="text-gray-600">Doc Basis:</span>
            <span className="text-gray-900">NYSAES-001</span>
          </div>
          <div className="flex justify-between">
            <span className="text-gray-600">End Basis:</span>
            <span className="text-gray-900">NYSAES-003</span>
          </div>
          <div className="flex justify-between">
            <span className="text-gray-600">Title:</span>
            <span className="text-gray-900">Noiret Grape Variety</span>
          </div>
          <div className="flex justify-between">
            <span className="text-gray-600">Privilege Level:</span>
            <span className="text-gray-900">NYSAES-004-010</span>
          </div>
          <div className="flex justify-between">
            <span className="text-gray-600">Reports:</span>
            <span className="text-blue-600 hover:underline cursor-pointer">Agricultural Research Report</span>
          </div>
          <div className="flex justify-between">
            <span className="text-gray-600">Pages:</span>
            <span className="text-gray-900">{document.pageCount || 1}</span>
          </div>
          <div className="flex justify-between">
            <span className="text-gray-600">Highlights:</span>
            <span className="text-blue-600 hover:underline cursor-pointer">Wine grapes, Cornell University</span>
          </div>
        </div>
      </div>

      {/* Additional Fields */}
      <div>
        <h4 className="text-sm font-medium text-gray-900 mb-3">Additional Fields</h4>
        <div className="space-y-2 text-sm">
          <div className="flex justify-between">
            <span className="text-gray-600">Privilege Level:</span>
            <span className="text-gray-900">Non-Responsive</span>
          </div>
          <div className="flex justify-between">
            <span className="text-gray-600">Confidentiality:</span>
            <span className="text-gray-900">Public</span>
          </div>
          <div className="flex justify-between">
            <span className="text-gray-600">Language:</span>
            <span className="text-gray-900">English</span>
          </div>
          <div className="flex justify-between">
            <span className="text-gray-600">Page Count:</span>
            <span className="text-gray-900">{document.pageCount || 1}</span>
          </div>
        </div>
      </div>

      {/* Quick Actions */}
      <div>
        <h4 className="text-sm font-medium text-gray-900 mb-3">Quick Actions</h4>
        <div className="space-y-2">
          <Button variant="outline" size="sm" className="w-full justify-start">
            <Download className="h-4 w-4 mr-2" />
            Export Document
          </Button>
          <Button variant="outline" size="sm" className="w-full justify-start">
            <Plus className="h-4 w-4 mr-2" />
            Add to Production
          </Button>
          <Button variant="outline" size="sm" className="w-full justify-start">
            <Flag className="h-4 w-4 mr-2" />
            Flag for Review
          </Button>
        </div>
      </div>

      {/* Notes & Annotations */}
      <div>
        <h4 className="text-sm font-medium text-gray-900 mb-3">Notes & Annotations</h4>
        <div className="text-sm text-gray-500">
          No annotations yet
        </div>
      </div>
    </div>
  );
}

// Document List Table Component
function DocumentListTable({
  documents,
  onDocumentSelect,
  selectedDocument,
  totalDocuments,
  searchTerm,
  highlightSearchTerm
}: {
  documents: Document[];
  onDocumentSelect: (document: Document) => void;
  selectedDocument: Document | null;
  totalDocuments?: number;
  searchTerm?: string;
  highlightSearchTerm?: (text: string, searchTerm: string) => React.ReactNode;
}) {

  const columns = [
    {
      key: 'id',
      label: 'ID',
      sortable: true,
      width: '80px'
    },
    {
      key: 'family',
      label: 'FAMILY',
      sortable: true,
      width: '100px'
    },
    {
      key: 'title',
      label: 'TITLE',
      sortable: true,
      render: (value: string) => (
        <div className="flex items-center space-x-2">
          <FileText className="h-4 w-4 text-gray-400" />
          <span className="truncate">
            {highlightSearchTerm && searchTerm ? highlightSearchTerm(value, searchTerm) : value}
          </span>
        </div>
      )
    },
    {
      key: 'mark',
      label: 'MARK',
      sortable: true,
      render: (value: string) => (
        value ? (
          <Badge
            variant="secondary"
            className="bg-red-100 text-red-800 border-red-200"
          >
            {value}
          </Badge>
        ) : null
      )
    },
    {
      key: 'issues',
      label: 'ISSUES',
      sortable: true
    },
    {
      key: 'reviewer',
      label: 'REVIEWER',
      sortable: true,
      render: (value: string) => (
        <div className="flex items-center space-x-2">
          <User className="h-4 w-4 text-gray-400" />
          <span>{value}</span>
        </div>
      )
    },
    {
      key: 'size',
      label: 'SIZE',
      sortable: true,
      width: '100px'
    }
  ];

  return (
    <div className="space-y-4">
      <div className="flex items-center justify-between">
        <h3 className="text-lg font-medium text-gray-900">Documents</h3>
        <div className="flex items-center space-x-2 text-sm text-gray-600">
          <span>Showing {documents.length}{totalDocuments ? ` of ${totalDocuments}` : ''} documents</span>
          {searchTerm && (
            <span className="text-blue-600">
              (filtered by "{searchTerm}")
            </span>
          )}
        </div>
      </div>

      <div className="overflow-x-auto overflow-y-visible">
        {documents.length === 0 ? (
          <div className="text-center py-12">
            <Search className="h-12 w-12 mx-auto text-gray-400 mb-4" />
            <h3 className="text-lg font-medium text-gray-900 mb-2">No documents found</h3>
            <p className="text-gray-600 mb-4">
              {searchTerm
                ? `No documents match your search for "${searchTerm}"`
                : "No documents available"
              }
            </p>
            {searchTerm && (
              <Button
                variant="outline"
                size="sm"
                onClick={() => {
                  // Clear search will be handled by refresh button
                  console.log('Clear search clicked');
                }}
              >
                Clear search
              </Button>
            )}
          </div>
        ) : (
          <table className="min-w-full divide-y divide-gray-200">
            <thead className="bg-gray-50">
              <tr>
                {columns.map((column) => (
                  <th
                    key={column.key}
                    className="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
                    style={{ width: column.width }}
                  >
                    {column.label}
                  </th>
                ))}
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {documents.map((document) => (
                <tr
                  key={document.id}
                  className={cn(
                    "hover:bg-gray-50 cursor-pointer transition-colors",
                    selectedDocument?.id === document.id && "bg-blue-50 border-l-4 border-blue-500"
                  )}
                  onClick={() => onDocumentSelect(document)}
                >
                  {columns.map((column) => (
                    <td key={column.key} className="px-4 py-2 whitespace-nowrap text-sm text-gray-900">
                      {column.render
                        ? column.render(document[column.key as keyof Document] as string)
                        : document[column.key as keyof Document]
                      }
                    </td>
                  ))}
                </tr>
              ))}
            </tbody>
          </table>
        )}
      </div>

      {/* Pagination */}
      <div className="flex items-center justify-between">
        <div className="text-sm text-gray-700">
          Showing {documents.length} of {totalDocuments || documents.length} results
        </div>
        <div className="flex items-center space-x-2">
          <Button variant="outline" size="sm" disabled>
            <ChevronLeft className="h-4 w-4" />
            Previous
          </Button>
          <Button variant="outline" size="sm" disabled>
            Next
            <ChevronRight className="h-4 w-4" />
          </Button>
        </div>
      </div>
    </div>
  );
}
