'use client';

import React, { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { cn } from '@/lib/utils';
import {
  ChevronDown,
  ChevronRight,
  Folder,
  FolderPlus,
  FolderOpen,
  Plus,
  FileText,
  Save,
  ArrowLeft,
  Calendar,
  Upload,
  X,
  CheckCircle,
  AlertCircle,
  Bell
} from 'lucide-react';
import {
  apiClient,
  formatDateForApi,
  formatDateFromApi,
  type CaseCreateRequest,
  type CaseResponse,
  type CollectionCreateRequest,
  type CollectionResponse,
  type TreeNode as ApiTreeNode
} from '@/lib/api';

interface TreeNode {
  id: string;
  name: string;
  type: 'collection' | 'case';
  children?: TreeNode[];
  expanded?: boolean;
}

interface CaseFormData {
  internalCaseNumber: string;
  caseName: string;
  dateOfLoss: string;
  dateOfLawsuit: string;
  dateOfNOC: string;
  dateReceivedFromCarrier: string;
}

interface UploadedFile {
  id: string;
  name: string;
  size: number;
  type: string;
  file: File;
  uploadProgress: number;
  status: 'pending' | 'uploading' | 'completed' | 'error';
  errorMessage?: string;
  serverFileId?: string; // ID returned from server after successful upload
}

interface Notification {
  id: string;
  type: 'success' | 'error' | 'info' | 'warning';
  title: string;
  message: string;
  duration?: number;
}

export default function CreateCasePage() {
  const router = useRouter();
  const [treeData, setTreeData] = useState<TreeNode[]>([
    {
      id: 'claim-file-log',
      name: 'Claim File Log',
      type: 'collection',
      expanded: true,
      children: []
    },
    {
      id: 'insurance-company-docs',
      name: 'Insurance Company docs',
      type: 'collection',
      expanded: false,
      children: []
    },
    {
      id: 'email',
      name: 'Email',
      type: 'collection',
      expanded: false,
      children: []
    },
    {
      id: 'policy',
      name: 'Policy',
      type: 'collection',
      expanded: false,
      children: []
    },
    {
      id: 'medical-records',
      name: 'Medical Records',
      type: 'collection',
      expanded: false,
      children: []
    },
    {
      id: 'claims',
      name: 'Claims',
      type: 'collection',
      expanded: false,
      children: []
    },
    {
      id: 'medical-bills',
      name: 'Medical Bills',
      type: 'collection',
      expanded: false,
      children: []
    },
    {
      id: 'lawsuit-docs',
      name: 'Lawsuit Docs',
      type: 'collection',
      expanded: false,
      children: []
    }
  ]);

  const [selectedNode, setSelectedNode] = useState<string | null>(null);
  const [formData, setFormData] = useState<CaseFormData>({
    internalCaseNumber: '',
    caseName: '',
    dateOfLoss: '',
    dateOfLawsuit: '',
    dateOfNOC: '',
    dateReceivedFromCarrier: ''
  });

  const [errors, setErrors] = useState<Partial<CaseFormData>>({});
  const [caseCreated, setCaseCreated] = useState(false);
  const [createdCaseId, setCreatedCaseId] = useState<string | null>(null);
  const [createdCaseData, setCreatedCaseData] = useState<CaseResponse | null>(null);
  const [uploadedFiles, setUploadedFiles] = useState<UploadedFile[]>([]);
  const [isDragOver, setIsDragOver] = useState(false);
  const [draggedNode, setDraggedNode] = useState<string | null>(null);
  const [dropTarget, setDropTarget] = useState<string | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [apiError, setApiError] = useState<string | null>(null);
  const [isLoadingTree, setIsLoadingTree] = useState(true);
  const [notifications, setNotifications] = useState<Notification[]>([]);
  const [showCollectionModal, setShowCollectionModal] = useState(false);
  const [newCollectionName, setNewCollectionName] = useState('');
  const [collectionParentId, setCollectionParentId] = useState<string | null>(null);
  const [isCreatingCollection, setIsCreatingCollection] = useState(false);
  const [isRedirecting, setIsRedirecting] = useState(false);

  // Helper function to find node by ID recursively
  const findNodeById = (nodes: TreeNode[], id: string): TreeNode | null => {
    for (const node of nodes) {
      if (node.id === id) return node;
      if (node.children) {
        const found = findNodeById(node.children, id);
        if (found) return found;
      }
    }
    return null;
  };

  // Helper function to get all collections recursively
  const getAllCollections = (nodes: TreeNode[]): TreeNode[] => {
    const collections: TreeNode[] = [];
    for (const node of nodes) {
      if (node.type === 'collection') {
        collections.push(node);
        if (node.children) {
          collections.push(...getAllCollections(node.children));
        }
      }
    }
    return collections;
  };

  // Helper function to get the full path of a node
  const getNodePath = (nodeId: string | null): string => {
    if (!nodeId) return 'Root';

    const findPath = (nodes: TreeNode[], targetId: string, currentPath: string[] = []): string[] | null => {
      for (const node of nodes) {
        const newPath = [...currentPath, node.name];

        if (node.id === targetId) {
          return newPath;
        }

        if (node.children) {
          const result = findPath(node.children, targetId, newPath);
          if (result) return result;
        }
      }
      return null;
    };

    const path = findPath(treeData, nodeId);
    return path ? path.join(' › ') : 'Unknown';
  };

  // Notification functions
  const showNotification = (notification: Omit<Notification, 'id'>) => {
    const id = `notification-${Date.now()}-${Math.random()}`;
    const newNotification: Notification = {
      ...notification,
      id,
      duration: notification.duration || 5000
    };

    setNotifications(prev => [...prev, newNotification]);

    // Auto remove after duration
    setTimeout(() => {
      removeNotification(id);
    }, newNotification.duration);
  };

  const removeNotification = (id: string) => {
    setNotifications(prev => prev.filter(n => n.id !== id));
  };

  // Load tree structure from backend
  const loadTreeStructure = async () => {
    try {
      setIsLoadingTree(true);
      setApiError(null);

      const treeStructure = await apiClient.getTreeStructure();

      // Convert API tree nodes to local tree nodes
      const convertApiTreeNode = (apiNode: ApiTreeNode): TreeNode => ({
        id: apiNode.id,
        name: apiNode.name,
        type: apiNode.type as 'collection' | 'case',
        expanded: apiNode.expanded,
        children: apiNode.children?.map(convertApiTreeNode),
      });

      const localTreeData = treeStructure.map(convertApiTreeNode);
      setTreeData(localTreeData);

    } catch (error) {
      console.error('Failed to load tree structure:', error);
      setApiError(error instanceof Error ? error.message : 'Failed to load data');

      showNotification({
        type: 'error',
        title: 'Failed to Load Data',
        message: 'Could not load collections and cases. Please refresh the page.'
      });
    } finally {
      setIsLoadingTree(false);
    }
  };

  // Load tree structure on component mount
  useEffect(() => {
    loadTreeStructure();
  }, []);

  // Check if all uploads are completed and redirect to all cases page
  useEffect(() => {
    if (caseCreated && uploadedFiles.length > 0) {
      const allCompleted = uploadedFiles.every(file =>
        file.status === 'completed' || file.status === 'error'
      );
      const hasCompletedFiles = uploadedFiles.some(file => file.status === 'completed');

      if (allCompleted && hasCompletedFiles && !isRedirecting) {
        setIsRedirecting(true);

        // Show final success notification
        showNotification({
          type: 'success',
          title: 'All Files Uploaded Successfully!',
          message: 'Redirecting to All Cases page...',
          duration: 2000
        });

        // Redirect after a short delay
        setTimeout(() => {
          router.push('/all-cases');
        }, 2000);
      }
    }
  }, [uploadedFiles, caseCreated, router, isRedirecting]);

  // Handle collection creation from modal
  const handleCreateCollection = async () => {
    // Prevent double execution
    if (isCreatingCollection) {
      console.log('Collection creation already in progress, skipping...');
      return;
    }

    if (!newCollectionName.trim()) {
      showNotification({
        type: 'warning',
        title: 'Collection Name Required',
        message: 'Please enter a name for the collection.'
      });
      return;
    }

    setIsCreatingCollection(true);
    setApiError(null);

    try {
      // Prepare collection data for API
      const collectionData: CollectionCreateRequest = {
        name: newCollectionName.trim(),
        parent_id: collectionParentId || undefined,
        is_expanded: false,
      };

      // Create the collection via API
      const createdCollection = await apiClient.createCollection(collectionData);

      // Create new collection node for local tree
      const newNode: TreeNode = {
        id: createdCollection.id,
        name: createdCollection.name,
        type: 'collection',
        children: [],
        expanded: false
      };

      const addNodeToTree = (nodes: TreeNode[]): TreeNode[] => {
        if (!collectionParentId) {
          // Add to root level
          return [...nodes, newNode];
        }

        return nodes.map(node => {
          if (node.id === collectionParentId) {
            return {
              ...node,
              children: [...(node.children || []), newNode],
              expanded: true
            };
          }
          if (node.children) {
            return { ...node, children: addNodeToTree(node.children) };
          }
          return node;
        });
      };

      setTreeData(addNodeToTree(treeData));

      // Show success notification
      showNotification({
        type: 'success',
        title: 'Collection Created',
        message: `Collection "${newCollectionName}" has been created successfully.`
      });

      // Reset modal state
      setShowCollectionModal(false);
      setNewCollectionName('');
      setCollectionParentId(null);

    } catch (error) {
      console.error('Failed to create collection:', error);
      setApiError(error instanceof Error ? error.message : 'Failed to create collection');

      showNotification({
        type: 'error',
        title: 'Failed to Create Collection',
        message: error instanceof Error ? error.message : 'An unexpected error occurred'
      });
    } finally {
      setIsCreatingCollection(false);
    }
  };

  // Notification component
  const NotificationToast = ({ notification }: { notification: Notification }) => {
    const getIcon = () => {
      switch (notification.type) {
        case 'success':
          return <CheckCircle className="h-5 w-5 text-green-600" />;
        case 'error':
          return <AlertCircle className="h-5 w-5 text-red-600" />;
        case 'warning':
          return <AlertCircle className="h-5 w-5 text-yellow-600" />;
        case 'info':
          return <Bell className="h-5 w-5 text-blue-600" />;
        default:
          return <Bell className="h-5 w-5 text-gray-600" />;
      }
    };

    const getBgColor = () => {
      switch (notification.type) {
        case 'success':
          return 'bg-green-50 border-green-200';
        case 'error':
          return 'bg-red-50 border-red-200';
        case 'warning':
          return 'bg-yellow-50 border-yellow-200';
        case 'info':
          return 'bg-blue-50 border-blue-200';
        default:
          return 'bg-gray-50 border-gray-200';
      }
    };

    return (
      <div
        className={cn(
          "flex items-start space-x-3 p-4 rounded-xl border shadow-lg backdrop-blur-sm",
          "transition-all duration-500 ease-in-out",
          "transform translate-x-0 opacity-100 scale-100",
          "animate-in slide-in-from-right-full",
          getBgColor()
        )}
      >
        <div className="flex-shrink-0 mt-0.5">
          {getIcon()}
        </div>
        <div className="flex-1 min-w-0">
          <h4 className="text-sm font-semibold text-gray-900">
            {notification.title}
          </h4>
          <p className="text-sm text-gray-600 mt-1">
            {notification.message}
          </p>
        </div>
        <button
          onClick={() => removeNotification(notification.id)}
          className="flex-shrink-0 p-1 hover:bg-white/50 rounded-lg transition-colors"
        >
          <X className="h-4 w-4 text-gray-400 hover:text-gray-600" />
        </button>
      </div>
    );
  };

  // Collection Creation Modal Component
  const CollectionModal = () => {
    if (!showCollectionModal) return null;

    const handleClose = () => {
      setShowCollectionModal(false);
      setNewCollectionName('');
      setCollectionParentId(null);
    };

    return (
      <div
        className="fixed inset-0 bg-black/50 backdrop-blur-sm z-50 flex items-center justify-center p-4"
        onClick={(e) => {
          if (e.target === e.currentTarget) {
            handleClose();
          }
        }}
      >
        <div className="bg-white rounded-2xl shadow-2xl max-w-md w-full p-6 transform transition-all">
          <div className="flex items-center justify-between mb-6">
            <h3 className="text-xl font-bold text-gray-900">Create New Collection</h3>
            <button
              onClick={handleClose}
              className="p-2 hover:bg-gray-100 rounded-lg transition-colors"
            >
              <X className="h-5 w-5 text-gray-500" />
            </button>
          </div>

          <div className="space-y-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Collection Name
              </label>
              <input
                type="text"
                value={newCollectionName}
                onChange={(e) => setNewCollectionName(e.target.value)}
                placeholder="Enter collection name..."
                className="w-full px-4 py-3 border border-gray-300 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors"
                autoFocus
              />
            </div>

            {collectionParentId && (
              <div className="text-sm text-gray-600 bg-blue-50 p-3 rounded-lg">
                <span className="font-medium">Parent Collection:</span>{' '}
                {findNodeById(treeData, collectionParentId)?.name || 'Unknown'}
              </div>
            )}
          </div>

          <div className="flex justify-end space-x-3 mt-6">
            <button
              onClick={handleClose}
              className="px-4 py-2 text-gray-600 hover:text-gray-800 font-medium transition-colors"
            >
              Cancel
            </button>
            <button
              onClick={(e) => {
                e.preventDefault();
                e.stopPropagation();
                handleCreateCollection();
              }}
              disabled={!newCollectionName.trim() || isCreatingCollection}
              className={cn(
                "px-6 py-2 rounded-xl font-medium transition-all flex items-center space-x-2",
                newCollectionName.trim() && !isCreatingCollection
                  ? "bg-blue-600 hover:bg-blue-700 text-white shadow-lg hover:shadow-xl"
                  : "bg-gray-200 text-gray-400 cursor-not-allowed"
              )}
            >
              {isCreatingCollection && (
                <div className="w-4 h-4 border-2 border-white/30 border-t-white rounded-full animate-spin" />
              )}
              <span>{isCreatingCollection ? 'Creating...' : 'Create Collection'}</span>
            </button>
          </div>
        </div>
      </div>
    );
  };

  // File upload functions
  const handleFileSelect = (files: FileList | null) => {
    if (!files) return;

    const maxSize = 50 * 1024 * 1024; // 50MB in bytes
    const validFiles: UploadedFile[] = [];
    const rejectedFiles: string[] = [];

    Array.from(files).forEach(file => {
      // Check file type
      if (file.type !== 'application/pdf') {
        rejectedFiles.push(`${file.name}: Only PDF files are allowed`);
        return;
      }

      // Check file size
      if (file.size > maxSize) {
        rejectedFiles.push(`${file.name}: File size must be less than 50MB`);
        return;
      }

      // File is valid
      validFiles.push({
        id: `file-${Date.now()}-${Math.random()}`,
        name: file.name,
        size: file.size,
        type: file.type,
        file,
        uploadProgress: 0,
        status: 'pending' as const
      });
    });

    // Show notification for rejected files
    if (rejectedFiles.length > 0) {
      showNotification({
        type: 'warning',
        title: 'Some Files Rejected',
        message: rejectedFiles.join('\n')
      });
    }

    // Add valid files
    if (validFiles.length > 0) {
      setUploadedFiles(prev => [...prev, ...validFiles]);

      // Don't start uploading immediately - files will be uploaded after case is created
      // If case is already created, upload the files now
      if (createdCaseId) {
        validFiles.forEach(file => uploadFile(file, createdCaseId));
      }
    }
  };

  const uploadFile = async (fileData: UploadedFile, caseId?: string) => {
    console.log(`Starting upload for ${fileData.name}`);

    // Use provided caseId or fall back to state
    const targetCaseId = caseId || createdCaseId;

    // Check if we have a created case to upload to
    if (!targetCaseId) {
      setUploadedFiles(prev =>
        prev.map(f => f.id === fileData.id ? {
          ...f,
          status: 'error',
          errorMessage: 'Please create a case first before uploading files.'
        } : f)
      );
      return;
    }

    try {
      // Mark as uploading
      setUploadedFiles(prev =>
        prev.map(f => f.id === fileData.id ? { ...f, status: 'uploading' } : f)
      );

      // Upload file to backend
      const response = await apiClient.uploadFile(
        targetCaseId,
        fileData.file,
        (progress) => {
          setUploadedFiles(prev =>
            prev.map(f => f.id === fileData.id ? { ...f, uploadProgress: progress } : f)
          );
        }
      );

      // Mark as completed
      setUploadedFiles(prev =>
        prev.map(f => f.id === fileData.id ? {
          ...f,
          status: 'completed',
          uploadProgress: 100,
          serverFileId: response.file_id
        } : f)
      );

      console.log(`File ${fileData.name} uploaded successfully:`, response);

      showNotification({
        type: 'success',
        title: 'File Uploaded',
        message: `${fileData.name} has been uploaded successfully.`
      });

    } catch (error) {
      console.error(`Upload failed for ${fileData.name}:`, error);

      setUploadedFiles(prev =>
        prev.map(f => f.id === fileData.id ? {
          ...f,
          status: 'error',
          errorMessage: error instanceof Error ? error.message : 'Upload failed. Please try again.'
        } : f)
      );

      showNotification({
        type: 'error',
        title: 'Upload Failed',
        message: `Failed to upload ${fileData.name}. ${error instanceof Error ? error.message : 'Please try again.'}`
      });
    }
  };

  const removeFile = (fileId: string) => {
    setUploadedFiles(prev => prev.filter(f => f.id !== fileId));
  };

  const retryUpload = (fileId: string) => {
    const fileToRetry = uploadedFiles.find(f => f.id === fileId);
    if (fileToRetry) {
      // Reset the file status and retry upload
      setUploadedFiles(prev =>
        prev.map(f => f.id === fileId ? {
          ...f,
          status: 'pending',
          uploadProgress: 0,
          errorMessage: undefined
        } : f)
      );
      uploadFile(fileToRetry, createdCaseId || undefined);
    }
  };

  const formatFileSize = (bytes: number): string => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  const resetForm = () => {
    setCaseCreated(false);
    setCreatedCaseId(null);
    setUploadedFiles([]);
    setFormData({
      internalCaseNumber: '',
      caseName: '',
      dateOfLoss: '',
      dateOfLawsuit: '',
      dateOfNOC: '',
      dateReceivedFromCarrier: ''
    });
    setErrors({});
    setSelectedNode(null);
  };

  // Drag and drop functions
  const handleDragStart = (nodeId: string, nodeType: 'collection' | 'case') => {
    // Only allow dragging cases, not collections
    if (nodeType === 'case') {
      setDraggedNode(nodeId);
    }
  };

  const handleDragOver = (e: React.DragEvent, targetNodeId: string, targetNodeType: 'collection' | 'case') => {
    e.preventDefault();
    // Only allow dropping on collections
    if (targetNodeType === 'collection' && draggedNode && draggedNode !== targetNodeId) {
      setDropTarget(targetNodeId);
    }
  };

  const handleDragLeave = () => {
    setDropTarget(null);
  };

  const handleDrop = (e: React.DragEvent, targetNodeId: string, targetNodeType: 'collection' | 'case') => {
    e.preventDefault();

    if (!draggedNode || targetNodeType !== 'collection' || draggedNode === targetNodeId) {
      setDraggedNode(null);
      setDropTarget(null);
      return;
    }

    // Move the dragged node to the target collection
    moveNodeToCollection(draggedNode, targetNodeId);

    setDraggedNode(null);
    setDropTarget(null);
  };

  const moveNodeToCollection = async (nodeId: string, targetCollectionId: string | null) => {

    // Find the node to move
    const nodeToMove = findNodeById(treeData, nodeId);
    if (!nodeToMove || nodeToMove.type !== 'case') {
      showNotification({
        type: 'error',
        title: 'Move Failed',
        message: 'Only cases can be moved between collections.'
      });
      return;
    }

    // If targetCollectionId is provided, verify target collection exists
    if (targetCollectionId) {
      const targetCollection = findNodeById(treeData, targetCollectionId);
      if (!targetCollection || targetCollection.type !== 'collection') {
        showNotification({
          type: 'error',
          title: 'Move Failed',
          message: 'Target collection not found. Please drop the case on a valid collection.'
        });
        return;
      }
    } else {
      console.log('DEBUG FRONTEND: Moving case to root level');
    }

    try {
      // Call the backend API to move the case
      await apiClient.moveCase(nodeId, targetCollectionId || undefined);

      // Update the local tree state after successful API call
      let updatedNodeToMove: TreeNode | null = null;

      const removeNodeFromTree = (nodes: TreeNode[]): TreeNode[] => {
        return nodes.filter(node => {
          if (node.id === nodeId) {
            updatedNodeToMove = node;
            return false;
          }
          if (node.children) {
            node.children = removeNodeFromTree(node.children);
          }
          return true;
        });
      };

      // Add the node to the target collection or root
      const addNodeToCollection = (nodes: TreeNode[]): TreeNode[] => {
        if (!targetCollectionId) {
          // Add to root level
          return [...nodes, updatedNodeToMove!];
        }

        return nodes.map(node => {
          if (node.id === targetCollectionId && node.type === 'collection' && updatedNodeToMove) {
            return {
              ...node,
              children: [...(node.children || []), updatedNodeToMove],
              expanded: true
            };
          }
          if (node.children) {
            return { ...node, children: addNodeToCollection(node.children) };
          }
          return node;
        });
      };

      // Update the tree data
      let updatedTree = removeNodeFromTree([...treeData]);
      if (updatedNodeToMove) {
        updatedTree = addNodeToCollection(updatedTree);
        setTreeData(updatedTree);

        // Show success notification
        const targetCollectionName = targetCollectionId
          ? findNodeById(updatedTree, targetCollectionId)?.name || 'Unknown Collection'
          : 'Root Level';

        showNotification({
          type: 'success',
          title: 'Case Moved Successfully',
          message: `Case "${nodeToMove.name}" has been moved to "${targetCollectionName}".`
        });
      }

    } catch (error) {
      console.error('Failed to move case:', error);
      showNotification({
        type: 'error',
        title: 'Move Failed',
        message: error instanceof Error ? error.message : 'Failed to move case. Please try again.'
      });
    }
  };

  // Keyboard shortcuts
  React.useEffect(() => {
    const handleKeyDown = (event: KeyboardEvent) => {
      // Save shortcut (Ctrl/Cmd + S)
      if ((event.ctrlKey || event.metaKey) && event.key === 's') {
        event.preventDefault();
        handleSave();
      }

      // Cancel shortcut (Escape)
      if (event.key === 'Escape') {
        event.preventDefault();
        window.history.back();
      }

      // Move case shortcut (Ctrl/Cmd + M)
      if ((event.ctrlKey || event.metaKey) && event.key === 'm' && selectedNode) {
        event.preventDefault();
        const selectedNodeData = findNodeById(treeData, selectedNode);
        if (selectedNodeData?.type === 'case') {
          // Show available collections for moving
          const collections = getAllCollections(treeData);
          const collectionNames = collections.map((col: TreeNode, index: number) => `${index + 1}. ${col.name}`).join('\n');
          const choice = prompt(`Move case "${selectedNodeData.name}" to:\n\n${collectionNames}\n\nEnter collection number:`);

          if (choice && !isNaN(parseInt(choice))) {
            const collectionIndex = parseInt(choice) - 1;
            if (collectionIndex >= 0 && collectionIndex < collections.length) {
              moveNodeToCollection(selectedNode, collections[collectionIndex].id);
            } else {
              showNotification({
                type: 'error',
                title: 'Invalid Selection',
                message: 'Please enter a valid collection number.'
              });
            }
          }
        }
      }
    };

    document.addEventListener('keydown', handleKeyDown);
    return () => document.removeEventListener('keydown', handleKeyDown);
  }, [formData]); // Include formData in dependencies so handleSave has latest values

  const toggleNode = (nodeId: string) => {
    const updateNode = (nodes: TreeNode[]): TreeNode[] => {
      return nodes.map(node => {
        if (node.id === nodeId) {
          return { ...node, expanded: !node.expanded };
        }
        if (node.children) {
          return { ...node, children: updateNode(node.children) };
        }
        return node;
      });
    };
    setTreeData(updateNode(treeData));
  };

  const addNewItem = (parentId: string | null, type: 'collection' | 'case') => {
    if (type === 'collection') {
      // Open modal for collection creation
      setCollectionParentId(parentId);
      setShowCollectionModal(true);
      return;
    }

    const newItem: TreeNode = {
      id: `${type}-${Date.now()}`,
      name: formData.caseName.trim(),
      type,
      children: undefined,
      expanded: false
    };

    if (parentId === null) {
      // Add to root
      setTreeData([...treeData, newItem]);
    } else {
      // Add to specific parent
      const updateNode = (nodes: TreeNode[]): TreeNode[] => {
        return nodes.map(node => {
          if (node.id === parentId && node.type === 'collection') {
            return {
              ...node,
              children: [...(node.children || []), newItem],
              expanded: true
            };
          }
          if (node.children) {
            return { ...node, children: updateNode(node.children) };
          }
          return node;
        });
      };
      setTreeData(updateNode(treeData));
    }

    // Handle post-creation actions for cases
    // Note: Form clearing and success notification are handled by handleSave function
    // This function only handles the tree structure update
  };

  const handleInputChange = (field: keyof CaseFormData, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }));
    // Clear error when user starts typing
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: undefined }));
    }
  };

  const formatCaseNumber = (value: string) => {
    // Remove all non-digits
    const digits = value.replace(/\D/g, '');
    // Format as 11111-11111
    if (digits.length <= 5) {
      return digits;
    } else {
      return `${digits.slice(0, 5)}-${digits.slice(5, 10)}`;
    }
  };

  const validateForm = (): boolean => {
    const newErrors: Partial<CaseFormData> = {};

    if (!formData.dateReceivedFromCarrier) {
      newErrors.dateReceivedFromCarrier = 'Date Received from Carrier is required';
    }

    if (formData.caseName.length > 256) {
      newErrors.caseName = 'Case name must be 256 characters or less';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSave = async () => {
    if (!validateForm()) {
      return;
    }

    setIsLoading(true);
    setApiError(null);

    try {
      // Determine where to create the case
      let targetParentId = selectedNode;

      // If no node is selected or selected node is a case, add to root
      if (!selectedNode) {
        targetParentId = null;
      } else {
        const selectedNodeData = findNodeById(treeData, selectedNode);
        if (selectedNodeData?.type === 'case') {
          // Can't add case under another case, add to root instead
          targetParentId = null;
        }
      }

      // Store case name and path for notification before clearing form
      const caseName = formData.caseName;
      const selectedCollectionPath = getNodePath(targetParentId);

      // Prepare case data for API
      const caseData: CaseCreateRequest = {
        case_name: formData.caseName,
        internal_case_number: formData.internalCaseNumber || undefined,
        date_of_loss: formatDateForApi(formData.dateOfLoss),
        date_of_lawsuit: formatDateForApi(formData.dateOfLawsuit),
        date_of_noc: formatDateForApi(formData.dateOfNOC),
        date_received_from_carrier: formatDateForApi(formData.dateReceivedFromCarrier)!,
        collection_id: targetParentId || undefined,
      };

      // Create the case via API
      const createdCase = await apiClient.createCase(caseData);

      // Update local tree structure
      addNewItem(targetParentId, 'case');

      // Store created case data
      setCreatedCaseId(createdCase.id);
      setCreatedCaseData(createdCase);
      setCaseCreated(true);

      // Clear the form after successful creation
      setFormData({
        internalCaseNumber: '',
        caseName: '',
        dateOfLoss: '',
        dateOfLawsuit: '',
        dateOfNOC: '',
        dateReceivedFromCarrier: ''
      });
      setErrors({});

      // Show success notification
      const hasFilesToUpload = uploadedFiles.some(file =>
        file.status === 'pending' || file.status === 'error'
      );

      showNotification({
        type: 'success',
        title: 'Case Created Successfully!',
        message: hasFilesToUpload
          ? `Case "${caseName}" created in ${selectedCollectionPath}. Files are being uploaded.`
          : `Case "${caseName}" created in ${selectedCollectionPath}.`
      });

      // Upload all pending files after case is created
      const filesToUpload = uploadedFiles.filter(file =>
        file.status === 'pending' || file.status === 'error'
      );

      if (filesToUpload.length === 0) {
        // No files to upload, redirect immediately
        setIsRedirecting(true);

        showNotification({
          type: 'success',
          title: 'Case Created Successfully!',
          message: 'Redirecting to All Cases page...',
          duration: 2000
        });

        setTimeout(() => {
          router.push('/all-cases');
        }, 2000);
      } else {
        // Upload files
        for (const file of filesToUpload) {
          // Reset error status and upload
          if (file.status === 'error') {
            setUploadedFiles(prev =>
              prev.map(f => f.id === file.id ? {
                ...f,
                status: 'pending',
                errorMessage: undefined,
                uploadProgress: 0
              } : f)
            );
          }
          await uploadFile(file, createdCase.id);
        }
      }

    } catch (error) {
      console.error('Failed to create case:', error);
      setApiError(error instanceof Error ? error.message : 'Failed to create case');

      showNotification({
        type: 'error',
        title: 'Failed to Create Case',
        message: error instanceof Error ? error.message : 'An unexpected error occurred'
      });
    } finally {
      setIsLoading(false);
    }
  };

  const renderTreeNode = (node: TreeNode, level: number = 0): React.ReactNode => {
    const isExpanded = node.expanded;
    const isSelected = selectedNode === node.id;
    const isDropTarget = dropTarget === node.id;
    const isDragging = draggedNode === node.id;

    return (
      <div key={node.id}>
        <div
          draggable={node.type === 'case'}
          onDragStart={() => handleDragStart(node.id, node.type)}
          onDragOver={(e) => handleDragOver(e, node.id, node.type)}
          onDragLeave={handleDragLeave}
          onDrop={(e) => handleDrop(e, node.id, node.type)}
          title={node.type === 'case' ? `Drag to move "${node.name}" to another collection` : undefined}
          className={cn(
            "flex items-center py-3 px-3 text-sm cursor-pointer rounded-xl transition-all duration-200",
            "hover:bg-gradient-to-r hover:shadow-md",
            isSelected
              ? "bg-gradient-to-r from-blue-500 to-indigo-500 text-white shadow-lg transform scale-[1.02]"
              : node.type === 'collection'
                ? "hover:from-blue-50 hover:to-indigo-50 hover:border-blue-200"
                : "hover:from-green-50 hover:to-emerald-50 hover:border-green-200",
            "border border-transparent",
            isDropTarget && node.type === 'collection' && "border-2 border-dashed border-blue-500 bg-blue-50",
            isDragging && "opacity-50 transform scale-95",
            node.type === 'case' && "cursor-move"
          )}
          style={{ paddingLeft: `${level * 20 + 12}px` }}
          onClick={() => setSelectedNode(node.id)}
        >
          {/* Expand/collapse button */}
          {node.type === 'collection' && (
            <button
              className={cn(
                "mr-2 p-1 rounded-lg transition-colors",
                isSelected ? "hover:bg-white/20" : "hover:bg-gray-100"
              )}
              onClick={(e) => {
                e.stopPropagation();
                toggleNode(node.id);
              }}
            >
              {isExpanded ? (
                <ChevronDown className={cn("h-4 w-4", isSelected ? "text-white" : "text-gray-600")} />
              ) : (
                <ChevronRight className={cn("h-4 w-4", isSelected ? "text-white" : "text-gray-600")} />
              )}
            </button>
          )}

          {/* Icon */}
          {node.type === 'collection' ? (
            isExpanded ? (
              <FolderOpen className={cn(
                "h-5 w-5 mr-3 transition-colors",
                isSelected ? "text-white" : isDropTarget ? "text-blue-600" : "text-blue-500"
              )} />
            ) : (
              <Folder className={cn(
                "h-5 w-5 mr-3 transition-colors",
                isSelected ? "text-white" : isDropTarget ? "text-blue-600" : "text-blue-500"
              )} />
            )
          ) : (
            <FileText className={cn(
              "h-5 w-5 mr-3 transition-colors",
              isSelected ? "text-white" : isDragging ? "text-gray-400" : "text-green-600"
            )} />
          )}

          {/* Name */}
          <span className={cn("flex-1 truncate font-medium", isSelected ? "text-white" : "text-gray-900")}>
            {node.name}
          </span>

          {/* Add buttons */}
          {isSelected && (
            <div className="flex items-center space-x-1 ml-2">
              {node.type === 'collection' && (
                <>
                  <button
                    onClick={(e) => {
                      e.stopPropagation();
                      addNewItem(node.id, 'collection');
                    }}
                    className={cn(
                      "p-1.5 rounded-lg transition-colors",
                      isSelected ? "hover:bg-white/20" : "hover:bg-blue-100"
                    )}
                    title="Add Sub-Collection"
                  >
                    <FolderPlus className={cn("h-4 w-4", isSelected ? "text-white" : "text-blue-600")} />
                  </button>
                  {/* <button
                    onClick={(e) => {
                      e.stopPropagation();
                      addNewItem(node.id, 'case');
                    }}
                    className={cn(
                      "p-1.5 rounded-lg transition-colors",
                      isSelected ? "hover:bg-white/20" : "hover:bg-green-100"
                    )}
                    title="Add Case to this Collection"
                  >
                    <Plus className={cn("h-4 w-4", isSelected ? "text-white" : "text-green-600")} />
                  </button> */}
                </>
              )}
              {node.type === 'case' && (
                <span className={cn("text-xs px-2 py-1 rounded-full", isSelected ? "text-white/80" : "text-gray-500")}>
                  Case selected
                </span>
              )}
              {isDropTarget && node.type === 'collection' && (
                <span className="text-xs px-2 py-1 rounded-full bg-blue-100 text-blue-700 font-medium">
                  Drop here
                </span>
              )}
            </div>
          )}
        </div>

        {/* Children */}
        {node.type === 'collection' && isExpanded && node.children && (
          <div>
            {node.children.map(child => renderTreeNode(child, level + 1))}
          </div>
        )}
      </div>
    );
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-50">
      {/* Redirecting Overlay */}
      {isRedirecting && (
        <div className="fixed inset-0 bg-black/50 backdrop-blur-sm z-50 flex items-center justify-center">
          <div className="bg-white rounded-2xl shadow-2xl p-8 max-w-md w-full mx-4 text-center">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
            <h3 className="text-xl font-bold text-gray-900 mb-2">Success!</h3>
            <p className="text-gray-600">Redirecting to All Cases page...</p>
          </div>
        </div>
      )}

      {/* Modern Header with Gradient */}
      <div className="bg-white/80 backdrop-blur-sm border-b border-gray-200/50 shadow-sm sticky top-0 z-10">
        <div className="max-w-7xl mx-auto px-6 py-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-6">
              <Button
                variant="ghost"
                size="sm"
                onClick={() => window.history.back()}
                className="text-gray-600 hover:text-gray-900 hover:bg-gray-100/50"
              >
                <ArrowLeft className="h-4 w-4 mr-2" />
                Back to Documents
              </Button>
              <div>
                <h1 className="text-3xl font-bold bg-gradient-to-r from-gray-900 to-gray-600 bg-clip-text text-transparent">
                  Create New Case
                </h1>
                <p className="text-sm text-gray-500 mt-1">Set up a new case and organize your documents</p>
              </div>
            </div>
            <div className="flex items-center space-x-3">
              <Button
                variant="outline"
                size="sm"
                onClick={() => window.location.href = '/all-cases'}
                className="border-blue-200 text-blue-700 hover:bg-blue-50"
              >
                <FileText className="h-4 w-4 mr-2" />
                All Cases
              </Button>
            </div>
          </div>
        </div>
      </div>

      {/* Main Content */}
      <div className="max-w-7xl mx-auto px-6 py-8">
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* Left Sidebar - Tree Structure */}
          <div className="lg:col-span-1">
            <div className="bg-white/70 backdrop-blur-sm overflow-hidden">
              {/* Tree Header */}
              <div className="p-6 bg-gradient-to-r from-slate-50 to-gray-50 border-b border-gray-100">
                <div className="flex items-center justify-between">
                  <div>
                    <h3 className="text-lg font-bold text-gray-900">Collections & Cases</h3>
                    <p className="text-sm text-gray-600 mt-1">Select a collection to organize your case</p>
                    <p className="text-xs text-blue-600 mt-1">💡 Drag cases to move them between collections</p>
                    <p className="text-xs text-gray-500 mt-1">⌨️ Or select a case and press Ctrl+M to move</p>
                  </div>
                  <div className="flex items-center space-x-2">
                    <button
                      onClick={() => addNewItem(null, 'collection')}
                      className="p-2 hover:bg-white/80 rounded-lg transition-colors shadow-sm border border-gray-200"
                      title="Add Root Collection"
                    >
                      <FolderPlus className="h-4 w-4 text-blue-600" />
                    </button>
                  </div>
                </div>
              </div>

              {/* Tree Content */}
              <div className="p-4 overflow-y-auto">
                {isLoadingTree ? (
                  <div className="flex items-center justify-center py-8">
                    <div className="text-center">
                      <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-2"></div>
                      <p className="text-sm text-gray-600">Loading collections and cases...</p>
                    </div>
                  </div>
                ) : treeData.length > 0 ? (
                  <div className="space-y-2">
                    {treeData.map(node => renderTreeNode(node))}
                  </div>
                ) : (
                  <div className="text-center py-8">
                    <Folder className="h-12 w-12 text-gray-300 mx-auto mb-3" />
                    <p className="text-sm text-gray-500 mb-2">No collections yet</p>
                    <p className="text-xs text-gray-400">Create your first collection to organize cases</p>
                  </div>
                )}
              </div>
            </div>
          </div>

          {/* Right Side - Case Form */}
          <div className="lg:col-span-2">
            <div className="bg-white/70 backdrop-blur-sm overflow-hidden">
              <div className="p-8">
                <div className="mb-8">
                  <h2 className="text-2xl font-bold bg-gradient-to-r from-gray-900 to-gray-600 bg-clip-text text-transparent">
                    Case Information
                  </h2>
                  {selectedNode ? (
                    (() => {
                      const selectedNodeData = findNodeById(treeData, selectedNode);
                      if (selectedNodeData?.type === 'collection') {
                        const fullPath = getNodePath(selectedNode);
                        return (
                          <div className="mt-3 p-3 bg-blue-50 border border-blue-200 rounded-lg">
                            <p className="text-sm text-blue-800">
                              <span className="font-medium">Creating case in:</span>
                              <span className="ml-2 font-bold text-blue-900">{fullPath}</span>
                            </p>
                          </div>
                        );
                      } else if (selectedNodeData?.type === 'case') {
                        return (
                          <div className="mt-3 p-3 bg-amber-50 border border-amber-200 rounded-lg">
                            <p className="text-sm text-amber-800">
                              Case selected. New case will be created at <span className="font-bold text-amber-900">root level</span>
                            </p>
                          </div>
                        );
                      }
                      return null;
                    })()
                  ) : (
                    <div className="mt-3 p-3 bg-gray-50 border border-gray-200 rounded-lg">
                      <p className="text-sm text-gray-600">
                        Select a collection to organize your case, or create at <span className="font-medium text-gray-900">root level</span>
                      </p>
                    </div>
                  )}
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
                  {/* Internal Case Number */}
                  <div className="space-y-2">
                    <Label htmlFor="internalCaseNumber" className="text-sm font-semibold text-gray-800">
                      Internal Case Number
                    </Label>
                    <Input
                      id="internalCaseNumber"
                      type="text"
                      placeholder="11111-11111"
                      value={formData.internalCaseNumber}
                      onChange={(e) => {
                        const formatted = formatCaseNumber(e.target.value);
                        handleInputChange('internalCaseNumber', formatted);
                      }}
                      maxLength={11}
                      className="h-12 border-gray-300 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all"
                    />
                    <p className="text-xs text-gray-500">Format: 11111-11111</p>
                  </div>

                  {/* Case Name */}
                  <div className="space-y-2">
                    <Label htmlFor="caseName" className="text-sm font-semibold text-gray-800">
                      Case Name <span className="text-red-500">*</span>
                    </Label>
                    <Input
                      id="caseName"
                      type="text"
                      placeholder="Enter case name"
                      value={formData.caseName}
                      onChange={(e) => handleInputChange('caseName', e.target.value)}
                      maxLength={256}
                      className={cn(
                        "h-12 border-gray-300 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all",
                        errors.caseName && "border-red-500 focus:ring-red-500"
                      )}
                    />
                    <div className="flex justify-between">
                      <p className="text-xs text-gray-500">Up to 256 characters</p>
                      <p className={cn("text-xs", formData.caseName.length > 240 ? "text-amber-600" : "text-gray-500")}>
                        {formData.caseName.length}/256
                      </p>
                    </div>
                    {errors.caseName && (
                      <p className="text-xs text-red-600 flex items-center">
                        <AlertCircle className="h-3 w-3 mr-1" />
                        {errors.caseName}
                      </p>
                    )}
                  </div>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-8 mt-8">
                  {/* Date of Loss */}
                  <div className="space-y-2">
                    <Label htmlFor="dateOfLoss" className="text-sm font-semibold text-gray-800">
                      Date of Loss
                    </Label>
                    <div className="relative">
                      <Input
                        id="dateOfLoss"
                        type="date"
                        value={formData.dateOfLoss}
                        onChange={(e) => handleInputChange('dateOfLoss', e.target.value)}
                        className="h-12 border-gray-300 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all pr-12"
                      />
                      <Calendar className="absolute right-4 top-1/2 transform -translate-y-1/2 h-5 w-5 text-gray-400 pointer-events-none" />
                    </div>
                    <p className="text-xs text-gray-500">Optional - if known</p>
                  </div>

                  {/* Date of Lawsuit */}
                  <div className="space-y-2">
                    <Label htmlFor="dateOfLawsuit" className="text-sm font-semibold text-gray-800">
                      Date of Lawsuit
                    </Label>
                    <div className="relative">
                      <Input
                        id="dateOfLawsuit"
                        type="date"
                        value={formData.dateOfLawsuit}
                        onChange={(e) => handleInputChange('dateOfLawsuit', e.target.value)}
                        className="h-12 border-gray-300 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all pr-12"
                      />
                      <Calendar className="absolute right-4 top-1/2 transform -translate-y-1/2 h-5 w-5 text-gray-400 pointer-events-none" />
                    </div>
                    <p className="text-xs text-gray-500">Optional - if known</p>
                  </div>

                  {/* Date of NOC */}
                  <div className="space-y-2">
                    <Label htmlFor="dateOfNOC" className="text-sm font-semibold text-gray-800">
                      Date of NOC
                    </Label>
                    <div className="relative">
                      <Input
                        id="dateOfNOC"
                        type="date"
                        value={formData.dateOfNOC}
                        onChange={(e) => handleInputChange('dateOfNOC', e.target.value)}
                        className="h-12 border-gray-300 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all pr-12"
                      />
                      <Calendar className="absolute right-4 top-1/2 transform -translate-y-1/2 h-5 w-5 text-gray-400 pointer-events-none" />
                    </div>
                    <p className="text-xs text-gray-500">Optional - if known</p>
                  </div>

                  {/* Date Received from Carrier - Required */}
                  <div className="space-y-2">
                    <Label htmlFor="dateReceivedFromCarrier" className="text-sm font-semibold text-gray-800">
                      Date Received from Carrier <span className="text-red-500">*</span>
                    </Label>
                    <div className="relative">
                      <Input
                        id="dateReceivedFromCarrier"
                        type="date"
                        value={formData.dateReceivedFromCarrier}
                        onChange={(e) => handleInputChange('dateReceivedFromCarrier', e.target.value)}
                        className={cn(
                          "h-12 border-gray-300 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all pr-12",
                          errors.dateReceivedFromCarrier && "border-red-500 focus:ring-red-500"
                        )}
                        required
                      />
                      <Calendar className="absolute right-4 top-1/2 transform -translate-y-1/2 h-5 w-5 text-gray-400 pointer-events-none" />
                    </div>
                    <p className="text-xs text-gray-500">Required field</p>
                    {errors.dateReceivedFromCarrier && (
                      <p className="text-xs text-red-600 flex items-center">
                        <AlertCircle className="h-3 w-3 mr-1" />
                        {errors.dateReceivedFromCarrier}
                      </p>
                    )}
                  </div>
                </div>
              </div>

              {/* API Error Display */}
              {apiError && (
                <div className="mt-6 p-4 bg-red-50 border border-red-200 rounded-lg">
                  <div className="flex items-center">
                    <AlertCircle className="h-5 w-5 text-red-500 mr-2" />
                    <p className="text-sm text-red-700">{apiError}</p>
                  </div>
                </div>
              )}

              {/* PDF Upload Section - Always visible, but uploads after case creation */}
              <div className="mt-8 pt-6 border-t border-gray-200">
                <div className="mb-8">
                  <h3 className="text-2xl font-bold bg-gradient-to-r from-gray-900 to-gray-600 bg-clip-text text-transparent mb-3">
                    Upload PDF Documents
                  </h3>
                  <p className="text-gray-600">
                    Upload PDF documents related to this case. You can drag and drop files or click to browse. Files will be uploaded after the case is created.
                  </p>
                </div>

                {/* File Drop Zone */}
                <div
                  className={cn(
                    "border-2 border-dashed rounded-2xl p-12 text-center transition-all duration-300 cursor-pointer",
                    isDragOver
                      ? "border-blue-500 bg-gradient-to-br from-blue-50 to-indigo-50 scale-[1.02] shadow-lg"
                      : "border-gray-300 hover:border-blue-400 hover:bg-gradient-to-br hover:from-gray-50 hover:to-blue-50"
                  )}
                  onDragOver={(e) => {
                    e.preventDefault();
                    setIsDragOver(true);
                  }}
                  onDragLeave={(e) => {
                    e.preventDefault();
                    setIsDragOver(false);
                  }}
                  onDrop={(e) => {
                    e.preventDefault();
                    setIsDragOver(false);
                    handleFileSelect(e.dataTransfer.files);
                  }}
                  onClick={() => document.getElementById('file-upload')?.click()}
                >
                  <div className={cn(
                    "p-4 rounded-full mx-auto mb-6 transition-colors",
                    isDragOver ? "bg-blue-100" : "bg-gray-100"
                  )}>
                    <Upload className={cn(
                      "h-12 w-12 mx-auto transition-colors",
                      isDragOver ? "text-blue-600" : "text-gray-400"
                    )} />
                  </div>
                  <h4 className="text-xl font-bold text-gray-900 mb-3">
                    Drop PDF files here, or click to browse
                  </h4>
                  <p className="text-gray-500 mb-6 max-w-md mx-auto">
                    Only PDF files are supported. Maximum file size: 50MB per file.
                  </p>
                  <input
                    type="file"
                    multiple
                    accept=".pdf,application/pdf"
                    onChange={(e) => handleFileSelect(e.target.files)}
                    className="hidden"
                    id="file-upload"
                  />
                  <Button
                    variant="outline"
                    className="h-12 px-8 rounded-xl border-2 border-blue-500 text-blue-600 hover:bg-blue-500 hover:text-white transition-all"
                    onClick={(e) => {
                      e.stopPropagation();
                      document.getElementById('file-upload')?.click();
                    }}
                  >
                    <Upload className="h-5 w-5 mr-3" />
                    Browse Files
                  </Button>
                </div>

                {/* Uploaded Files List */}
                {uploadedFiles.length > 0 && (
                  <div className="mt-8">
                    <h4 className="text-xl font-bold text-gray-900 mb-6 flex items-center">
                      <FileText className="h-6 w-6 mr-2 text-blue-600" />
                      Files to Upload ({uploadedFiles.length})
                    </h4>
                    <div className="space-y-4">
                      {uploadedFiles.map((file) => (
                        <div
                          key={file.id}
                          className="flex items-center justify-between p-6 bg-white/50 backdrop-blur-sm rounded-2xl border border-gray-200 shadow-sm hover:shadow-md transition-all"
                        >
                          <div className="flex items-center space-x-4 flex-1">
                            <div className="p-3 bg-red-100 rounded-xl">
                              <FileText className="h-8 w-8 text-red-600" />
                            </div>
                            <div className="flex-1 min-w-0">
                              <p className="text-sm font-semibold text-gray-900 truncate">
                                {file.name}
                              </p>
                              <p className="text-xs text-gray-500 mt-1">
                                {formatFileSize(file.size)}
                              </p>
                            </div>
                          </div>

                          <div className="flex items-center space-x-4">
                            {/* Status and Progress */}
                            {file.status === 'pending' && (
                              <div className="flex items-center space-x-2">
                                <div className="w-2 h-2 bg-yellow-400 rounded-full animate-pulse"></div>
                                <span className="text-xs font-medium text-yellow-600">Pending upload</span>
                              </div>
                            )}
                            {file.status === 'uploading' && (
                              <div className="flex items-center space-x-3">
                                <div className="w-24 bg-gray-200 rounded-full h-2">
                                  <div
                                    className="bg-gradient-to-r from-blue-500 to-indigo-500 h-2 rounded-full transition-all duration-300"
                                    style={{ width: `${file.uploadProgress}%` }}
                                  ></div>
                                </div>
                                <span className="text-xs font-medium text-blue-600">{file.uploadProgress}%</span>
                              </div>
                            )}
                            {file.status === 'completed' && (
                              <div className="flex items-center space-x-2">
                                <CheckCircle className="h-5 w-5 text-green-500" />
                                <span className="text-xs font-medium text-green-600">Uploaded</span>
                              </div>
                            )}
                            {file.status === 'error' && (
                              <div className="flex items-center space-x-2">
                                <AlertCircle className="h-5 w-5 text-red-500" />
                                <span className="text-xs text-red-600 font-medium">{file.errorMessage}</span>
                              </div>
                            )}

                            {/* Action buttons */}
                            <div className="flex items-center space-x-1">
                              {file.status === 'error' && (
                                <button
                                  onClick={() => retryUpload(file.id)}
                                  className="p-2 hover:bg-blue-100 rounded-xl transition-colors group"
                                  title="Retry upload"
                                >
                                  <svg className="h-4 w-4 text-gray-400 group-hover:text-blue-500 transition-colors" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
                                  </svg>
                                </button>
                              )}
                              <button
                                onClick={() => removeFile(file.id)}
                                className="p-2 hover:bg-red-100 rounded-xl transition-colors group"
                                title="Remove file"
                              >
                                <X className="h-4 w-4 text-gray-400 group-hover:text-red-500 transition-colors" />
                              </button>
                            </div>
                          </div>
                        </div>
                      ))}
                    </div>
                  </div>
                )}
              </div>

              {/* Create Case Button - now below upload section */}
              {!caseCreated && (
                <div className="mt-8 flex justify-end">
                  <Button
                    onClick={handleSave}
                    disabled={isLoading}
                    className="bg-blue-600 hover:bg-blue-700 disabled:opacity-50"
                    title="Create Case (Ctrl+S)"
                  >
                    <Save className="h-4 w-4 mr-2" />
                    {isLoading ? 'Creating...' : 'Create Case'}
                  </Button>
                </div>
              )}

              {/* Final Actions */}
              {caseCreated && (
                <div className="mt-12 pt-8 border-t border-gray-200">
                  <div className="flex flex-col sm:flex-row justify-between items-center space-y-4 sm:space-y-0">
                    <Button
                      variant="outline"
                      onClick={resetForm}
                      className="h-12 px-6 rounded-xl border-2 border-gray-300 hover:border-blue-500 hover:text-blue-600 transition-all"
                    >
                      <Plus className="h-4 w-4 mr-2" />
                      Create Another Case
                    </Button>
                    <div className="flex space-x-4">
                      <Button
                        variant="outline"
                        onClick={() => window.location.href = '/documents_review'}
                        className="h-12 px-6 rounded-xl border-2 border-gray-300 hover:border-indigo-500 hover:text-indigo-600 transition-all"
                      >
                        <FileText className="h-4 w-4 mr-2" />
                        View Documents
                      </Button>
                      <Button
                        className="h-12 px-8 rounded-xl bg-gradient-to-r from-green-600 to-emerald-600 hover:from-green-700 hover:to-emerald-700 text-white shadow-lg hover:shadow-xl transition-all"
                        onClick={() => {
                          const completedCount = uploadedFiles.filter(f => f.status === 'completed').length;
                          showNotification({
                            type: 'success',
                            title: 'Case Completed Successfully!',
                            message: `${completedCount} documents uploaded for case "${formData.caseName}"`
                          });
                          setTimeout(() => {
                            window.location.href = '/documents_review';
                          }, 2000);
                        }}
                      >
                        <CheckCircle className="h-5 w-5 mr-2" />
                        Complete Case Setup
                      </Button>
                    </div>
                  </div>
                </div>
              )}
            </div>
          </div>
        </div>
      </div>

      {/* Notification Container */}
      <div className="fixed top-4 right-4 z-50 space-y-3 max-w-md">
        {notifications.map((notification) => (
          <NotificationToast key={notification.id} notification={notification} />
        ))}
      </div>

      {/* Collection Creation Modal */}
      <CollectionModal />
    </div>
  );
}
