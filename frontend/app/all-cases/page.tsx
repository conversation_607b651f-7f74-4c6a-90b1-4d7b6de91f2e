'use client';

import React, { useState, useEffect } from 'react';
import { apiClient, CaseWithProcessingStatus } from '@/lib/api';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Progress } from '@/components/ui/progress';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import {
  Search,
  Filter,
  FileText,
  Calendar,
  FolderOpen,
  AlertCircle,
  CheckCircle,
  Clock,
  XCircle,
  Eye,
  Edit,
  MoreHorizontal,
  Plus,
  List,
  Grid3X3
} from 'lucide-react';
import { cn } from '@/lib/utils';

interface Notification {
  id: string;
  type: 'success' | 'error' | 'info' | 'warning';
  title: string;
  message: string;
  duration?: number;
}

const AllCasesPage = () => {
  const [cases, setCases] = useState<CaseWithProcessingStatus[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState<string>('all');
  const [viewMode, setViewMode] = useState<'list' | 'grid'>('list');
  const [notifications, setNotifications] = useState<Notification[]>([]);

  // Load cases on component mount
  useEffect(() => {
    loadCases();
  }, []);

  const loadCases = async () => {
    try {
      setLoading(true);
      const casesData = await apiClient.getCasesWithStatus();
      setCases(casesData);
    } catch (error) {
      console.error('Error loading cases:', error);
      showNotification({
        type: 'error',
        title: 'Error',
        message: 'Failed to load cases. Please try again.'
      });
    } finally {
      setLoading(false);
    }
  };

  const showNotification = (notification: Omit<Notification, 'id'>) => {
    const id = Date.now().toString();
    const newNotification = { ...notification, id };
    setNotifications(prev => [...prev, newNotification]);

    // Auto-remove notification after duration
    setTimeout(() => {
      setNotifications(prev => prev.filter(n => n.id !== id));
    }, notification.duration || 5000);
  };

  // Filter cases based on search term and status
  const filteredCases = cases.filter(case_ => {
    const matchesSearch = case_.case_name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         case_.internal_case_number?.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         case_.collection_name?.toLowerCase().includes(searchTerm.toLowerCase());
    
    const matchesStatus = statusFilter === 'all' || case_.processing_status === statusFilter;
    
    return matchesSearch && matchesStatus;
  });

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'completed':
        return <CheckCircle className="h-4 w-4 text-green-600" />;
      case 'in_progress':
        return <Clock className="h-4 w-4 text-blue-600" />;
      case 'error':
        return <XCircle className="h-4 w-4 text-red-600" />;
      case 'not_started':
        return <AlertCircle className="h-4 w-4 text-gray-400" />;
      default:
        return <AlertCircle className="h-4 w-4 text-gray-400" />;
    }
  };

  const getStatusBadge = (status: string) => {
    const variants = {
      completed: 'bg-green-100 text-green-800 border-green-200',
      in_progress: 'bg-blue-100 text-blue-800 border-blue-200',
      error: 'bg-red-100 text-red-800 border-red-200',
      not_started: 'bg-gray-100 text-gray-800 border-gray-200'
    };

    const labels = {
      completed: 'Completed',
      in_progress: 'In Progress',
      error: 'Error',
      not_started: 'Not Started'
    };

    return (
      <Badge className={cn('border', variants[status as keyof typeof variants])}>
        {getStatusIcon(status)}
        <span className="ml-1">{labels[status as keyof typeof labels]}</span>
      </Badge>
    );
  };

  const getProgressColor = (status: string, progress: number) => {
    if (status === 'error') return 'bg-red-500';
    if (status === 'completed') return 'bg-green-500';
    if (progress > 0) return 'bg-blue-500';
    return 'bg-gray-300';
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    });
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-100">
        <div className="max-w-7xl mx-auto px-6 py-8">
          <div className="flex items-center justify-center py-12">
            <div className="text-center">
              <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
              <p className="text-lg text-gray-600">Loading cases...</p>
            </div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-100">
      {/* Header */}
      <div className="bg-white/80 backdrop-blur-sm border-b border-gray-200">
        <div className="max-w-7xl mx-auto px-6 py-6">
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-3xl font-bold bg-gradient-to-r from-gray-900 to-gray-600 bg-clip-text text-transparent">
                All Cases
              </h1>
              <p className="text-sm text-gray-500 mt-1">Manage and monitor all your cases and their processing status</p>
            </div>
            <div className="flex items-center space-x-4">
              <Button
                onClick={() => window.location.href = '/create-case'}
                variant="default"
                className="bg-blue-600 hover:bg-blue-700 text-white"
              >
                <Plus className="h-4 w-4 mr-2" />
                Create Case
              </Button>
              <Button
                onClick={loadCases}
                variant="outline"
                className="border-blue-200 text-blue-700 hover:bg-blue-50"
              >
                Refresh
              </Button>
            </div>
          </div>
        </div>
      </div>

      {/* Main Content */}
      <div className="max-w-7xl mx-auto px-6 py-8">
        {/* Summary Statistics */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-8">
          <div className="bg-white/70 backdrop-blur-sm rounded-lg border border-gray-200 p-4">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <FileText className="h-8 w-8 text-blue-600" />
              </div>
              <div className="ml-3">
                <p className="text-sm font-medium text-gray-500">Total Cases</p>
                <p className="text-2xl font-semibold text-gray-900">{cases.length}</p>
              </div>
            </div>
          </div>

          <div className="bg-white/70 backdrop-blur-sm rounded-lg border border-gray-200 p-4">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <CheckCircle className="h-8 w-8 text-green-600" />
              </div>
              <div className="ml-3">
                <p className="text-sm font-medium text-gray-500">Completed</p>
                <p className="text-2xl font-semibold text-gray-900">
                  {cases.filter(c => c.processing_status === 'completed').length}
                </p>
              </div>
            </div>
          </div>

          <div className="bg-white/70 backdrop-blur-sm rounded-lg border border-gray-200 p-4">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <Clock className="h-8 w-8 text-blue-600" />
              </div>
              <div className="ml-3">
                <p className="text-sm font-medium text-gray-500">In Progress</p>
                <p className="text-2xl font-semibold text-gray-900">
                  {cases.filter(c => c.processing_status === 'in_progress').length}
                </p>
              </div>
            </div>
          </div>

          <div className="bg-white/70 backdrop-blur-sm rounded-lg border border-gray-200 p-4">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <XCircle className="h-8 w-8 text-red-600" />
              </div>
              <div className="ml-3">
                <p className="text-sm font-medium text-gray-500">With Errors</p>
                <p className="text-2xl font-semibold text-gray-900">
                  {cases.filter(c => c.processing_status === 'error').length}
                </p>
              </div>
            </div>
          </div>
        </div>

        {/* Filters */}
        <div className="mb-6 flex flex-col sm:flex-row gap-4">
          <div className="relative flex-1">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
            <Input
              placeholder="Search cases by name, number, or collection..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="pl-10 bg-white/70 backdrop-blur-sm border-gray-200"
            />
          </div>
          <div className="flex items-center space-x-4">
            <div className="flex items-center space-x-2">
              <Filter className="h-4 w-4 text-gray-500" />
              <select
                value={statusFilter}
                onChange={(e) => setStatusFilter(e.target.value)}
                className="px-3 py-2 border border-gray-200 rounded-md bg-white/70 backdrop-blur-sm text-sm"
              >
                <option value="all">All Status</option>
                <option value="not_started">Not Started</option>
                <option value="in_progress">In Progress</option>
                <option value="completed">Completed</option>
                <option value="error">Error</option>
              </select>
            </div>

            {/* View Toggle */}
            <div className="flex items-center border border-gray-200 rounded-md bg-white/70 backdrop-blur-sm">
              <Button
                variant={viewMode === 'list' ? 'default' : 'ghost'}
                size="sm"
                onClick={() => setViewMode('list')}
                className="rounded-r-none border-r border-gray-200"
              >
                <List className="h-4 w-4" />
              </Button>
              <Button
                variant={viewMode === 'grid' ? 'default' : 'ghost'}
                size="sm"
                onClick={() => setViewMode('grid')}
                className="rounded-l-none"
              >
                <Grid3X3 className="h-4 w-4" />
              </Button>
            </div>
          </div>
        </div>

        {/* Cases Display */}
        {filteredCases.length === 0 ? (
          <div className="text-center py-12">
            <FileText className="h-16 w-16 text-gray-300 mx-auto mb-4" />
            <h3 className="text-lg font-medium text-gray-900 mb-2">No cases found</h3>
            <p className="text-gray-500">
              {searchTerm || statusFilter !== 'all'
                ? 'Try adjusting your search or filter criteria.'
                : 'Create your first case to get started.'}
            </p>
          </div>
        ) : viewMode === 'list' ? (
          // List View
          <div className="bg-white/70 backdrop-blur-sm rounded-lg border border-gray-200 overflow-hidden">
            {/* Table Header - Hidden on mobile */}
            <div className="hidden lg:block bg-gray-50/80 px-6 py-4 border-b border-gray-200">
              <div className="grid grid-cols-12 gap-4 text-sm font-medium text-gray-700">
                <div className="col-span-3">Case Information</div>
                <div className="col-span-2">Collection</div>
                <div className="col-span-2">Status</div>
                <div className="col-span-3">Processing Progress</div>
                <div className="col-span-1">Files</div>
                <div className="col-span-1">Actions</div>
              </div>
            </div>

            {/* Table Body */}
            <div className="divide-y divide-gray-200">
              {filteredCases.map((case_) => (
                <div key={case_.id} className="px-4 lg:px-6 py-4 hover:bg-gray-50/50 transition-colors duration-150">
                  {/* Desktop Layout */}
                  <div className="hidden lg:grid lg:grid-cols-12 lg:gap-4 lg:items-center">
                    {/* Case Information */}
                    <div className="col-span-3">
                      <div className="flex flex-col">
                        <h3 className="font-semibold text-gray-900 truncate" title={case_.case_name}>
                          {case_.case_name}
                        </h3>
                        <p className="text-sm text-gray-500 mt-1">
                          {case_.internal_case_number || 'No case number'}
                        </p>
                        <p className="text-xs text-gray-400 mt-1">
                          Received: {formatDate(case_.date_received_from_carrier)}
                        </p>
                      </div>
                    </div>

                    {/* Collection */}
                    <div className="col-span-2">
                      {case_.collection_name ? (
                        <div className="flex items-center text-sm text-gray-600">
                          <FolderOpen className="h-4 w-4 mr-2 text-blue-500" />
                          <span className="truncate" title={case_.collection_name}>
                            {case_.collection_name}
                          </span>
                        </div>
                      ) : (
                        <span className="text-sm text-gray-400">No collection</span>
                      )}
                    </div>

                    {/* Status */}
                    <div className="col-span-2">
                      {getStatusBadge(case_.processing_status)}
                    </div>

                    {/* Processing Progress */}
                    <div className="col-span-3">
                      <div className="space-y-2">
                        <div className="flex items-center justify-between text-sm">
                          <span className="text-gray-600">Progress</span>
                          <span className="font-medium text-gray-900">{case_.processing_progress}%</span>
                        </div>
                        <Progress
                          value={case_.processing_progress}
                          className="h-2"
                        />
                        <div className="flex items-center justify-between text-xs text-gray-500">
                          <span>{case_.processed_files} of {case_.total_files} processed</span>
                          {case_.error_files > 0 && (
                            <span className="text-red-600 font-medium">{case_.error_files} errors</span>
                          )}
                        </div>
                      </div>
                    </div>

                    {/* Files Count */}
                    <div className="col-span-1">
                      <div className="flex items-center text-sm text-gray-600">
                        <FileText className="h-4 w-4 mr-1" />
                        <span className="font-medium">{case_.total_files}</span>
                      </div>
                    </div>

                    {/* Actions */}
                    <div className="col-span-1">
                      <div className="flex items-center space-x-1">
                        <Button size="sm" variant="ghost" className="h-8 w-8 p-0" title="View Case">
                          <Eye className="h-4 w-4" />
                        </Button>
                        <Button size="sm" variant="ghost" className="h-8 w-8 p-0" title="Edit Case">
                          <Edit className="h-4 w-4" />
                        </Button>
                        <Button size="sm" variant="ghost" className="h-8 w-8 p-0" title="More Options">
                          <MoreHorizontal className="h-4 w-4" />
                        </Button>
                      </div>
                    </div>
                  </div>

                  {/* Mobile Layout */}
                  <div className="lg:hidden space-y-3">
                    {/* Header Row */}
                    <div className="flex items-start justify-between">
                      <div className="flex-1 min-w-0">
                        <h3 className="font-semibold text-gray-900 truncate" title={case_.case_name}>
                          {case_.case_name}
                        </h3>
                        <p className="text-sm text-gray-500 mt-1">
                          {case_.internal_case_number || 'No case number'}
                        </p>
                      </div>
                      <div className="ml-3 flex-shrink-0">
                        {getStatusBadge(case_.processing_status)}
                      </div>
                    </div>

                    {/* Details Row */}
                    <div className="flex items-center justify-between text-sm text-gray-600">
                      <div className="flex items-center">
                        <Calendar className="h-4 w-4 mr-1" />
                        <span>{formatDate(case_.date_received_from_carrier)}</span>
                      </div>
                      {case_.collection_name && (
                        <div className="flex items-center">
                          <FolderOpen className="h-4 w-4 mr-1 text-blue-500" />
                          <span className="truncate max-w-32" title={case_.collection_name}>
                            {case_.collection_name}
                          </span>
                        </div>
                      )}
                    </div>

                    {/* Progress Row */}
                    <div className="space-y-2">
                      <div className="flex items-center justify-between text-sm">
                        <span className="text-gray-600">Processing Progress</span>
                        <span className="font-medium text-gray-900">{case_.processing_progress}%</span>
                      </div>
                      <Progress
                        value={case_.processing_progress}
                        className="h-2"
                      />
                      <div className="flex items-center justify-between text-xs text-gray-500">
                        <div className="flex items-center">
                          <FileText className="h-3 w-3 mr-1" />
                          <span>{case_.processed_files} of {case_.total_files} files processed</span>
                        </div>
                        {case_.error_files > 0 && (
                          <span className="text-red-600 font-medium">{case_.error_files} errors</span>
                        )}
                      </div>
                    </div>

                    {/* Actions Row */}
                    <div className="flex items-center justify-end space-x-2 pt-2 border-t border-gray-100">
                      <Button size="sm" variant="outline" className="text-xs">
                        <Eye className="h-3 w-3 mr-1" />
                        View
                      </Button>
                      <Button size="sm" variant="outline" className="text-xs">
                        <Edit className="h-3 w-3 mr-1" />
                        Edit
                      </Button>
                      <Button size="sm" variant="ghost" className="text-xs">
                        <MoreHorizontal className="h-3 w-3" />
                      </Button>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>
        ) : (
          // Grid View
          <div className="grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-6">
            {filteredCases.map((case_) => (
              <Card key={case_.id} className="bg-white/70 backdrop-blur-sm border-gray-200 hover:shadow-lg transition-all duration-200">
                <CardHeader className="pb-3">
                  <div className="flex items-start justify-between">
                    <div className="flex-1 min-w-0">
                      <CardTitle className="text-lg font-semibold text-gray-900 truncate">
                        {case_.case_name}
                      </CardTitle>
                      <p className="text-sm text-gray-500 mt-1">
                        {case_.internal_case_number || 'No case number'}
                      </p>
                    </div>
                    <div className="ml-2">
                      {getStatusBadge(case_.processing_status)}
                    </div>
                  </div>
                </CardHeader>
                <CardContent className="space-y-4">
                  {/* Processing Progress */}
                  <div className="space-y-2">
                    <div className="flex items-center justify-between text-sm">
                      <span className="text-gray-600">Document Processing</span>
                      <span className="font-medium text-gray-900">{case_.processing_progress}%</span>
                    </div>
                    <Progress
                      value={case_.processing_progress}
                      className="h-2"
                    />
                    <div className="flex items-center justify-between text-xs text-gray-500">
                      <span>{case_.processed_files} of {case_.total_files} files processed</span>
                      {case_.error_files > 0 && (
                        <span className="text-red-600">{case_.error_files} errors</span>
                      )}
                    </div>
                  </div>

                  {/* Case Details */}
                  <div className="space-y-2 text-sm">
                    {case_.collection_name && (
                      <div className="flex items-center text-gray-600">
                        <FolderOpen className="h-4 w-4 mr-2" />
                        <span className="truncate">{case_.collection_name}</span>
                      </div>
                    )}
                    <div className="flex items-center text-gray-600">
                      <Calendar className="h-4 w-4 mr-2" />
                      <span>Received: {formatDate(case_.date_received_from_carrier)}</span>
                    </div>
                    <div className="flex items-center text-gray-600">
                      <FileText className="h-4 w-4 mr-2" />
                      <span>{case_.total_files} document{case_.total_files !== 1 ? 's' : ''}</span>
                    </div>
                  </div>

                  {/* Actions */}
                  <div className="flex items-center justify-between pt-2 border-t border-gray-100">
                    <div className="flex items-center space-x-2">
                      <Button size="sm" variant="outline" className="text-xs">
                        <Eye className="h-3 w-3 mr-1" />
                        View
                      </Button>
                      <Button size="sm" variant="outline" className="text-xs">
                        <Edit className="h-3 w-3 mr-1" />
                        Edit
                      </Button>
                    </div>
                    <Button size="sm" variant="ghost" className="text-xs">
                      <MoreHorizontal className="h-3 w-3" />
                    </Button>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        )}
      </div>

      {/* Notifications */}
      <div className="fixed top-4 right-4 space-y-2 z-50">
        {notifications.map((notification) => (
          <div
            key={notification.id}
            className={cn(
              'p-4 rounded-lg shadow-lg border max-w-sm',
              {
                'bg-green-50 border-green-200 text-green-800': notification.type === 'success',
                'bg-red-50 border-red-200 text-red-800': notification.type === 'error',
                'bg-blue-50 border-blue-200 text-blue-800': notification.type === 'info',
                'bg-yellow-50 border-yellow-200 text-yellow-800': notification.type === 'warning',
              }
            )}
          >
            <div className="font-medium">{notification.title}</div>
            <div className="text-sm mt-1">{notification.message}</div>
          </div>
        ))}
      </div>
    </div>
  );
};

export default AllCasesPage;
