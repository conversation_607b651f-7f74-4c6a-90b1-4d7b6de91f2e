'use client';

import { useState, useEffect } from 'react';
import Navbar from '@/components/Navbar';
import { Card } from '@/components/ui/card';
import { FadeIn, SlideIn } from '@/components/ui/loading';
import {
  Shield,
  Users,
  TrendingUp,
  Award,
  CheckCircle,
  Star,
  Quote
} from 'lucide-react';

export default function AuthLayout({ children }: { children: React.ReactNode }) {
  const [currentTestimonial, setCurrentTestimonial] = useState(0);

  const testimonials = [
    {
      text: "This platform has revolutionized how we handle insurance litigation. The efficiency gains are remarkable.",
      author: "<PERSON>",
      role: "Senior Legal Counsel",
      company: "MetroLaw Firm",
      rating: 5
    },
    {
      text: "The collaboration features have made working with our legal team seamless and more productive.",
      author: "<PERSON>",
      role: "Claims Manager",
      company: "National Insurance Co.",
      rating: 5
    },
    {
      text: "Outstanding platform with excellent security features. Highly recommended for any legal practice.",
      author: "<PERSON>",
      role: "Partner",
      company: "Rodriguez & Associates",
      rating: 5
    }
  ];

  const features = [
    {
      icon: Shield,
      title: "Enterprise Security",
      description: "Bank-level encryption and compliance"
    },
    {
      icon: Users,
      title: "Team Collaboration",
      description: "Real-time collaboration tools"
    },
    {
      icon: TrendingUp,
      title: "Analytics & Insights",
      description: "Comprehensive reporting dashboard"
    },
    {
      icon: Award,
      title: "Industry Leading",
      description: "Trusted by 500+ law firms"
    }
  ];

  useEffect(() => {
    const interval = setInterval(() => {
      setCurrentTestimonial((prev) => (prev + 1) % testimonials.length);
    }, 5000);
    return () => clearInterval(interval);
  }, [testimonials.length]);

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-indigo-50 relative overflow-hidden">
      {/* Background Pattern */}
      <div className="absolute inset-0 opacity-5">
        <div className="absolute top-0 left-0 w-full h-full" style={{
          backgroundImage: `url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23000000' fill-opacity='0.1'%3E%3Ccircle cx='30' cy='30' r='2'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E")`
        }}></div>
      </div>

      {/* Floating Elements */}
      <div className="absolute top-20 left-10 w-20 h-20 bg-blue-200 rounded-full opacity-20 animate-bounce-in" style={{ animationDelay: '1s' }}></div>
      <div className="absolute top-40 right-20 w-16 h-16 bg-indigo-200 rounded-full opacity-20 animate-bounce-in" style={{ animationDelay: '1.5s' }}></div>
      <div className="absolute bottom-20 left-1/4 w-12 h-12 bg-purple-200 rounded-full opacity-20 animate-bounce-in" style={{ animationDelay: '2s' }}></div>

      {/* <Navbar /> */}

      <div className="flex min-h-[calc(100vh-4rem)] relative z-10">
        {/* Left Side - Branding & Features */}
        <div className="hidden lg:flex lg:w-1/2 xl:w-3/5 flex-col justify-center px-12 xl:px-16">
          <FadeIn delay={200}>
            <div className="max-w-lg">
              <div className="flex items-center mb-6">
                <div className="p-3 bg-blue-100 rounded-xl mr-4">
                  <Shield className="h-8 w-8 text-blue-600" />
                </div>
                <div>
                  <h1 className="text-2xl font-bold text-gray-900">Insurance Litigation</h1>
                  <p className="text-gray-600">Professional Platform</p>
                </div>
              </div>

              <h2 className="text-4xl font-bold text-gray-900 mb-6 leading-tight">
                Streamline Your Legal
                <span className="text-blue-600 block">Workflow</span>
              </h2>

              <p className="text-lg text-gray-600 mb-8">
                Join thousands of legal professionals who trust our platform for efficient
                case management, secure document handling, and seamless team collaboration.
              </p>

              {/* Features Grid */}
              <div className="grid grid-cols-2 gap-4 mb-8">
                {features.map((feature, index) => (
                  <SlideIn key={index} direction="up" delay={400 + index * 100}>
                    <div className="flex items-start space-x-3 p-3 rounded-lg hover:bg-white/50 transition-colors">
                      <div className="p-2 bg-blue-100 rounded-lg">
                        <feature.icon className="h-5 w-5 text-blue-600" />
                      </div>
                      <div>
                        <h3 className="font-semibold text-gray-900 text-sm">{feature.title}</h3>
                        <p className="text-xs text-gray-600">{feature.description}</p>
                      </div>
                    </div>
                  </SlideIn>
                ))}
              </div>

              {/* Testimonial */}
              <SlideIn direction="up" delay={800}>
                <Card className="p-6 glass-effect border-0 shadow-lg">
                  <div className="flex items-center mb-3">
                    <Quote className="h-5 w-5 text-blue-600 mr-2" />
                    <div className="flex">
                      {[...Array(testimonials[currentTestimonial].rating)].map((_, i) => (
                        <Star key={i} className="h-4 w-4 text-yellow-400 fill-current" />
                      ))}
                    </div>
                  </div>
                  <blockquote className="text-gray-700 mb-4 italic">
                    "{testimonials[currentTestimonial].text}"
                  </blockquote>
                  <div>
                    <div className="font-semibold text-gray-900">
                      {testimonials[currentTestimonial].author}
                    </div>
                    <div className="text-sm text-gray-600">
                      {testimonials[currentTestimonial].role}
                    </div>
                    <div className="text-sm text-blue-600">
                      {testimonials[currentTestimonial].company}
                    </div>
                  </div>
                </Card>
              </SlideIn>

              {/* Trust Indicators */}
              <SlideIn direction="up" delay={1000}>
                <div className="flex items-center space-x-6 mt-8 text-sm text-gray-600">
                  <div className="flex items-center">
                    <CheckCircle className="h-4 w-4 text-green-600 mr-2" />
                    <span>SOC 2 Compliant</span>
                  </div>
                  <div className="flex items-center">
                    <CheckCircle className="h-4 w-4 text-green-600 mr-2" />
                    <span>99.9% Uptime</span>
                  </div>
                  <div className="flex items-center">
                    <CheckCircle className="h-4 w-4 text-green-600 mr-2" />
                    <span>24/7 Support</span>
                  </div>
                </div>
              </SlideIn>
            </div>
          </FadeIn>
        </div>

        {/* Right Side - Auth Form */}
        <div className="w-full lg:w-1/2 xl:w-2/5 flex items-center justify-center px-6 py-12">
          <div className="w-full max-w-md">
            <SlideIn direction="right" delay={300}>
              <Card className="p-8 shadow-2xl border-0 glass-effect hover:shadow-3xl transition-all duration-500">
                {children}
              </Card>
            </SlideIn>
          </div>
        </div>
      </div>
    </div>
  );
}
