{"name": "frontend", "version": "1.0.0", "description": "", "main": "next.config.js", "scripts": {"dev": "next dev", "build": "next build", "start": "next start"}, "keywords": [], "author": "", "license": "ISC", "dependencies": {"@chakra-ui/react": "^3.22.0", "@emotion/react": "^11.14.0", "@emotion/styled": "^11.14.1", "@hookform/resolvers": "^5.1.1", "@mui/icons-material": "^7.2.0", "@mui/material": "^7.2.0", "@radix-ui/react-avatar": "^1.1.10", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-dropdown-menu": "^2.1.15", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-slot": "^1.2.3", "@tailwindcss/postcss": "^4.1.11", "@types/node": "^24.0.15", "@types/react": "^19.1.8", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "framer-motion": "^12.23.6", "lucide-react": "^0.525.0", "next": "^15.4.2", "react": "^19.1.0", "react-dom": "^19.1.0", "react-hook-form": "^7.60.0", "tailwind-merge": "^3.3.1", "typescript": "^5.8.3", "zod": "^4.0.5"}, "devDependencies": {"autoprefixer": "^10.4.21", "postcss": "^8.5.6", "tailwindcss": "^4.1.11", "tw-animate-css": "^1.3.5"}}