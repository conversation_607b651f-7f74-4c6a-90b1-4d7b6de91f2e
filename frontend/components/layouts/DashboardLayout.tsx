'use client';

import { useState, useEffect } from 'react';
import { usePathname } from 'next/navigation';
import Link from 'next/link';
import Navbar from '@/components/Navbar';
import { Button } from '@/components/ui/button';
import { Card } from '@/components/ui/card';
import { FadeIn, SlideIn } from '@/components/ui/loading';
import { SmartNotificationCenter } from '@/components/ui/smart-widgets';
import {
  Home,
  FileText,
  Users,
  BarChart3,
  Settings,
  Calendar,
  Search,
  Bell,
  ChevronRight,
  Menu,
  X,
  Briefcase,
  DollarSign,
  Clock,
  AlertTriangle,
  Plus,
  Filter,
  Download,
  RefreshCw
} from 'lucide-react';

interface DashboardLayoutProps {
  children: React.ReactNode;
  title?: string;
  subtitle?: string;
  actions?: React.ReactNode;
  breadcrumbs?: Array<{ label: string; href?: string }>;
}

interface Notification {
  id: string;
  type: 'success' | 'warning' | 'info' | 'urgent';
  title: string;
  message: string;
  timestamp: string;
  read: boolean;
  actionLabel?: string;
  actionUrl?: string;
}

export default function DashboardLayout({ 
  children, 
  title, 
  subtitle, 
  actions,
  breadcrumbs 
}: DashboardLayoutProps) {
  const pathname = usePathname();
  const [sidebarOpen, setSidebarOpen] = useState(false);
  const [notifications, setNotifications] = useState<Notification[]>([
    {
      id: '1',
      type: 'urgent',
      title: 'Urgent Case Review',
      message: 'Case #247 requires immediate attention',
      timestamp: '2 minutes ago',
      read: false,
      actionLabel: 'Review Case',
      actionUrl: '/dashboard/cases/247'
    },
    {
      id: '2',
      type: 'info',
      title: 'Document Uploaded',
      message: 'New evidence uploaded to Case #156',
      timestamp: '1 hour ago',
      read: false,
      actionLabel: 'View Document',
      actionUrl: '/documents_review'
    },
    {
      id: '3',
      type: 'success',
      title: 'Case Resolved',
      message: 'Case #189 has been successfully closed',
      timestamp: '3 hours ago',
      read: true
    }
  ]);

  const sidebarItems = [
    {
      label: 'Dashboard',
      href: '/protected',
      icon: Home,
      active: pathname === '/protected'
    },
    {
      label: 'Cases',
      href: '/dashboard/cases',
      icon: Briefcase,
      active: pathname.startsWith('/dashboard/cases'),
      badge: '23'
    },
    {
      label: 'Documents',
      href: '/documents_review',
      icon: FileText,
      active: pathname.startsWith('/documents_review')
    },
    {
      label: 'Team',
      href: '/dashboard/team',
      icon: Users,
      active: pathname.startsWith('/dashboard/team')
    },
    {
      label: 'Analytics',
      href: '/dashboard/analytics',
      icon: BarChart3,
      active: pathname.startsWith('/dashboard/analytics')
    },
    {
      label: 'Calendar',
      href: '/dashboard/calendar',
      icon: Calendar,
      active: pathname.startsWith('/dashboard/calendar')
    },
    {
      label: 'Settings',
      href: '/dashboard/settings',
      icon: Settings,
      active: pathname.startsWith('/dashboard/settings')
    }
  ];

  const handleMarkAsRead = (id: string) => {
    setNotifications(prev => 
      prev.map(n => n.id === id ? { ...n, read: true } : n)
    );
  };

  const handleDismiss = (id: string) => {
    setNotifications(prev => prev.filter(n => n.id !== id));
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-indigo-50">
      <Navbar />
      
      <div className="flex pt-16">
        {/* Sidebar */}
        <div className={`fixed inset-y-0 left-0 z-50 w-64 bg-white shadow-lg transform transition-transform duration-300 ease-in-out lg:translate-x-0 lg:static lg:inset-0 ${
          sidebarOpen ? 'translate-x-0' : '-translate-x-full'
        }`}>
          <div className="flex flex-col h-full pt-16 lg:pt-0">
            {/* Sidebar Header */}
            <div className="flex items-center justify-between p-4 border-b border-gray-200 lg:hidden">
              <h2 className="text-lg font-semibold text-gray-900">Menu</h2>
              <Button
                variant="ghost"
                size="sm"
                onClick={() => setSidebarOpen(false)}
              >
                <X className="h-5 w-5" />
              </Button>
            </div>

            {/* Navigation */}
            <nav className="flex-1 px-4 py-6 space-y-2">
              {sidebarItems.map((item, index) => (
                <FadeIn key={item.href} delay={index * 50}>
                  <Link
                    href={item.href}
                    className={`flex items-center justify-between px-3 py-2 rounded-lg text-sm font-medium transition-all duration-200 hover-lift ${
                      item.active
                        ? 'bg-blue-50 text-blue-700 border border-blue-200'
                        : 'text-gray-700 hover:bg-gray-50 hover:text-gray-900'
                    }`}
                    onClick={() => setSidebarOpen(false)}
                  >
                    <div className="flex items-center">
                      <item.icon className={`h-5 w-5 mr-3 ${
                        item.active ? 'text-blue-600' : 'text-gray-400'
                      }`} />
                      {item.label}
                    </div>
                    {item.badge && (
                      <span className="bg-red-100 text-red-800 text-xs font-medium px-2 py-1 rounded-full">
                        {item.badge}
                      </span>
                    )}
                  </Link>
                </FadeIn>
              ))}
            </nav>

            {/* Sidebar Footer */}
            <div className="p-4 border-t border-gray-200">
              <Card className="p-3 bg-gradient-to-r from-blue-50 to-indigo-50 border-blue-200">
                <div className="flex items-center text-sm">
                  <AlertTriangle className="h-4 w-4 text-blue-600 mr-2" />
                  <div>
                    <p className="font-medium text-blue-900">Need Help?</p>
                    <p className="text-blue-700 text-xs">Contact support</p>
                  </div>
                </div>
              </Card>
            </div>
          </div>
        </div>

        {/* Main Content */}
        <div className="flex-1 lg:ml-0">
          {/* Top Bar */}
          <div className="bg-white border-b border-gray-200 px-6 py-4">
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-4">
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => setSidebarOpen(true)}
                  className="lg:hidden"
                >
                  <Menu className="h-5 w-5" />
                </Button>

                <div>
                  {breadcrumbs && (
                    <nav className="flex items-center space-x-2 text-sm text-gray-500 mb-1">
                      {breadcrumbs.map((crumb, index) => (
                        <div key={index} className="flex items-center">
                          {index > 0 && <ChevronRight className="h-3 w-3 mx-1" />}
                          {crumb.href ? (
                            <Link href={crumb.href} className="hover:text-gray-700">
                              {crumb.label}
                            </Link>
                          ) : (
                            <span className="text-gray-900 font-medium">{crumb.label}</span>
                          )}
                        </div>
                      ))}
                    </nav>
                  )}
                  {title && (
                    <h1 className="text-2xl font-bold text-gray-900">{title}</h1>
                  )}
                  {subtitle && (
                    <p className="text-gray-600 mt-1">{subtitle}</p>
                  )}
                </div>
              </div>

              <div className="flex items-center space-x-4">
                {/* Search */}
                <div className="hidden md:block relative">
                  <Search className="absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 text-gray-400" />
                  <input
                    type="text"
                    placeholder="Search..."
                    className="pl-10 pr-4 py-2 w-64 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-300 hover-lift"
                  />
                </div>

                {/* Notifications */}
                <SmartNotificationCenter
                  notifications={notifications}
                  onMarkAsRead={handleMarkAsRead}
                  onDismiss={handleDismiss}
                />

                {/* Actions */}
                {actions}
              </div>
            </div>
          </div>

          {/* Page Content */}
          <div className="p-6">
            {children}
          </div>
        </div>
      </div>

      {/* Sidebar Overlay */}
      {sidebarOpen && (
        <div
          className="fixed inset-0 bg-black bg-opacity-50 z-40 lg:hidden"
          onClick={() => setSidebarOpen(false)}
        />
      )}
    </div>
  );
}
