'use client';

import { useState, useEffect } from 'react';
import Link from 'next/link';
import { Button } from '@/components/ui/button';
import { usePathname } from 'next/navigation';
import {
  Menu,
  X,
  Shield,
  User,
  Bell,
  Search,
  ChevronDown,
  LogOut,
  Settings,
  Home,
  FileText,
  Users
} from 'lucide-react';
import { FadeIn, SlideIn } from '@/components/ui/loading';

export default function Navbar() {
  const pathname = usePathname();
  const isLandingPage = pathname === '/';
  const isDashboard = pathname.startsWith('/dashboard');
  const [isScrolled, setIsScrolled] = useState(false);
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);
  const [isUserMenuOpen, setIsUserMenuOpen] = useState(false);
  const [isAuthenticated, setIsAuthenticated] = useState(false); // Mock auth state

  useEffect(() => {
    const handleScroll = () => {
      setIsScrolled(window.scrollY > 10);
    };

    if (isLandingPage) {
      window.addEventListener('scroll', handleScroll);
      return () => window.removeEventListener('scroll', handleScroll);
    }
  }, [isLandingPage]);

  // Mock authentication check
  useEffect(() => {
    // In a real app, this would check actual auth state
    setIsAuthenticated(isDashboard);
  }, [isDashboard]);

  const navItems = [
    { href: '/', label: 'Home', icon: Home, show: !isLandingPage },
    { href: '/dashboard', label: 'Dashboard', icon: FileText, show: true },
    { href: '/dashboard/claims', label: 'Claims', icon: FileText, show: isAuthenticated },
    { href: '/dashboard/team', label: 'Team', icon: Users, show: isAuthenticated },
  ];

  const userMenuItems = [
    { href: '/dashboard/settings', label: 'Settings', icon: Settings },
    { href: '/profile', label: 'Profile', icon: User },
    { href: '/auth/login', label: 'Sign Out', icon: LogOut },
  ];

  return (
    <>
      <nav className={`w-full transition-all duration-300 z-50 ${
        isLandingPage
          ? `fixed top-0 left-0 right-0 ${
              isScrolled
                ? 'bg-white/95 backdrop-blur-md shadow-lg border-b border-gray-200'
                : 'bg-transparent'
            }`
          : 'bg-white border-b border-gray-200 shadow-sm sticky top-0'
      }`}>
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex items-center justify-between h-16">
            {/* Logo */}
            <FadeIn delay={100}>
              <Link
                href="/"
                className={`flex items-center space-x-2 font-bold text-lg tracking-wide transition-colors duration-300 hover:scale-105 transform ${
                  isLandingPage && !isScrolled ? 'text-gray-900' : 'text-blue-900'
                }`}
              >
                <div className="p-2 bg-blue-100 rounded-lg">
                  <Shield className="h-6 w-6 text-blue-600" />
                </div>
                <span className="hidden sm:block">Insurance Litigation</span>
                <span className="sm:hidden">ILS</span>
              </Link>
            </FadeIn>

            {/* Desktop Navigation */}
            <div className="hidden md:flex items-center space-x-1">
              <SlideIn direction="down" delay={200}>
                <div className="flex items-center space-x-1">
                  {navItems.map((item, index) =>
                    item.show && (
                      <FadeIn key={item.href} delay={300 + index * 100}>
                        <Link href={item.href}>
                          <Button
                            variant="ghost"
                            className={`hover-lift transition-all duration-300 ${
                              pathname === item.href
                                ? 'bg-blue-50 text-blue-600 border border-blue-200'
                                : 'hover:bg-gray-50'
                            }`}
                          >
                            <item.icon className="h-4 w-4 mr-2" />
                            {item.label}
                          </Button>
                        </Link>
                      </FadeIn>
                    )
                  )}
                </div>
              </SlideIn>

              {/* Search Bar (for authenticated users) */}
              {isAuthenticated && (
                <SlideIn direction="down" delay={400}>
                  <div className="relative ml-4">
                    <Search className="absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 text-gray-400" />
                    <input
                      type="text"
                      placeholder="Search..."
                      className="pl-10 pr-4 py-2 w-64 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-300 hover-lift"
                    />
                  </div>
                </SlideIn>
              )}

              {/* Auth Buttons / User Menu */}
              <SlideIn direction="down" delay={500}>
                <div className="flex items-center space-x-2 ml-4">
                  {isAuthenticated ? (
                    <>
                      {/* Notifications */}
                      <Button variant="ghost" size="sm" className="relative hover-lift">
                        <Bell className="h-5 w-5" />
                        <span className="absolute -top-1 -right-1 h-3 w-3 bg-red-500 rounded-full animate-pulse"></span>
                      </Button>

                      {/* User Menu */}
                      <div className="relative">
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => setIsUserMenuOpen(!isUserMenuOpen)}
                          className="flex items-center space-x-2 hover-lift"
                        >
                          <div className="w-8 h-8 bg-gradient-to-br from-blue-500 to-blue-600 rounded-full flex items-center justify-center">
                            <User className="h-4 w-4 text-white" />
                          </div>
                          <ChevronDown className={`h-4 w-4 transition-transform duration-300 ${
                            isUserMenuOpen ? 'rotate-180' : ''
                          }`} />
                        </Button>

                        {/* User Dropdown */}
                        {isUserMenuOpen && (
                          <FadeIn delay={0}>
                            <div className="absolute right-0 mt-2 w-48 bg-white rounded-lg shadow-lg border border-gray-200 py-2 z-50">
                              <div className="px-4 py-2 border-b border-gray-100">
                                <p className="text-sm font-medium text-gray-900">John Doe</p>
                                <p className="text-xs text-gray-500"><EMAIL></p>
                              </div>
                              {userMenuItems.map((item, index) => (
                                <FadeIn key={item.href} delay={index * 50}>
                                  <Link
                                    href={item.href}
                                    className="flex items-center px-4 py-2 text-sm text-gray-700 hover:bg-gray-50 transition-colors"
                                    onClick={() => setIsUserMenuOpen(false)}
                                  >
                                    <item.icon className="h-4 w-4 mr-3" />
                                    {item.label}
                                  </Link>
                                </FadeIn>
                              ))}
                            </div>
                          </FadeIn>
                        )}
                      </div>
                    </>
                  ) : (
                    <>
                      <Link href="/auth/register">
                        <Button
                          variant={isLandingPage && !isScrolled ? "outline" : "ghost"}
                          className="hover-lift"
                        >
                          Register
                        </Button>
                      </Link>
                      <Link href="/auth/login">
                        <Button
                          variant={isLandingPage && !isScrolled ? "default" : "ghost"}
                          className="hover-lift bg-blue-600 text-white hover:bg-blue-700"
                        >
                          Login
                        </Button>
                      </Link>
                    </>
                  )}
                </div>
              </SlideIn>
            </div>

            {/* Mobile Menu Button */}
            <div className="md:hidden">
              <Button
                variant="ghost"
                size="sm"
                onClick={() => setIsMobileMenuOpen(!isMobileMenuOpen)}
                className="hover-lift"
              >
                {isMobileMenuOpen ? (
                  <X className="h-6 w-6" />
                ) : (
                  <Menu className="h-6 w-6" />
                )}
              </Button>
            </div>
          </div>
        </div>

        {/* Mobile Menu */}
        {isMobileMenuOpen && (
          <SlideIn direction="down" delay={0}>
            <div className="md:hidden bg-white border-t border-gray-200 shadow-lg">
              <div className="px-4 py-4 space-y-2">
                {navItems.map((item, index) =>
                  item.show && (
                    <FadeIn key={item.href} delay={index * 100}>
                      <Link
                        href={item.href}
                        className={`flex items-center px-3 py-2 rounded-lg text-sm font-medium transition-colors ${
                          pathname === item.href
                            ? 'bg-blue-50 text-blue-600'
                            : 'text-gray-700 hover:bg-gray-50'
                        }`}
                        onClick={() => setIsMobileMenuOpen(false)}
                      >
                        <item.icon className="h-4 w-4 mr-3" />
                        {item.label}
                      </Link>
                    </FadeIn>
                  )
                )}

                {!isAuthenticated && (
                  <div className="pt-4 border-t border-gray-200 space-y-2">
                    <Link href="/auth/register" onClick={() => setIsMobileMenuOpen(false)}>
                      <Button variant="outline" className="w-full justify-start hover-lift">
                        Register
                      </Button>
                    </Link>
                    <Link href="/auth/login" onClick={() => setIsMobileMenuOpen(false)}>
                      <Button className="w-full justify-start hover-lift bg-blue-600 text-white hover:bg-blue-700">
                        Login
                      </Button>
                    </Link>
                  </div>
                )}

                {isAuthenticated && (
                  <div className="pt-4 border-t border-gray-200">
                    <div className="flex items-center px-3 py-2 mb-2">
                      <div className="w-8 h-8 bg-gradient-to-br from-blue-500 to-blue-600 rounded-full flex items-center justify-center mr-3">
                        <User className="h-4 w-4 text-white" />
                      </div>
                      <div>
                        <p className="text-sm font-medium text-gray-900">John Doe</p>
                        <p className="text-xs text-gray-500"><EMAIL></p>
                      </div>
                    </div>
                    {userMenuItems.map((item, index) => (
                      <FadeIn key={item.href} delay={index * 50}>
                        <Link
                          href={item.href}
                          className="flex items-center px-3 py-2 rounded-lg text-sm text-gray-700 hover:bg-gray-50 transition-colors"
                          onClick={() => setIsMobileMenuOpen(false)}
                        >
                          <item.icon className="h-4 w-4 mr-3" />
                          {item.label}
                        </Link>
                      </FadeIn>
                    ))}
                  </div>
                )}
              </div>
            </div>
          </SlideIn>
        )}
      </nav>

      {/* Click outside to close menus */}
      {(isMobileMenuOpen || isUserMenuOpen) && (
        <div
          className="fixed inset-0 z-40"
          onClick={() => {
            setIsMobileMenuOpen(false);
            setIsUserMenuOpen(false);
          }}
        />
      )}
    </>
  );
}
