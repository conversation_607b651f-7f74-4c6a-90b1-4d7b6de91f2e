'use client';

import { useState, useEffect } from 'react';
import { Card } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { cn } from '@/lib/utils';
import {
  Bell,
  X,
  CheckCircle,
  AlertTriangle,
  Info,
  Clock,
  TrendingUp,
  TrendingDown,
  Calendar,
  FileText,
  Users,
  DollarSign,
  Activity,
  Zap,
  Target,
  Award
} from 'lucide-react';

interface Notification {
  id: string;
  type: 'success' | 'warning' | 'info' | 'urgent';
  title: string;
  message: string;
  timestamp: string;
  read: boolean;
  actionLabel?: string;
  actionUrl?: string;
}

interface SmartNotificationProps {
  notifications: Notification[];
  onMarkAsRead: (id: string) => void;
  onDismiss: (id: string) => void;
  className?: string;
}

export function SmartNotificationCenter({ 
  notifications, 
  onMarkAsRead, 
  onDismiss, 
  className 
}: SmartNotificationProps) {
  const [isOpen, setIsOpen] = useState(false);
  const unreadCount = notifications.filter(n => !n.read).length;

  const getNotificationIcon = (type: string) => {
    switch (type) {
      case 'success': return <CheckCircle className="h-5 w-5 text-green-500" />;
      case 'warning': return <AlertTriangle className="h-5 w-5 text-yellow-500" />;
      case 'urgent': return <AlertTriangle className="h-5 w-5 text-red-500" />;
      default: return <Info className="h-5 w-5 text-blue-500" />;
    }
  };

  const getNotificationColor = (type: string) => {
    switch (type) {
      case 'success': return 'border-l-green-500 bg-green-50';
      case 'warning': return 'border-l-yellow-500 bg-yellow-50';
      case 'urgent': return 'border-l-red-500 bg-red-50';
      default: return 'border-l-blue-500 bg-blue-50';
    }
  };

  return (
    <div className={cn("relative", className)}>
      <Button
        variant="ghost"
        size="sm"
        onClick={() => setIsOpen(!isOpen)}
        className="relative hover-lift"
      >
        <Bell className="h-5 w-5" />
        {unreadCount > 0 && (
          <Badge className="absolute -top-1 -right-1 h-5 w-5 p-0 text-xs bg-red-500 text-white">
            {unreadCount}
          </Badge>
        )}
      </Button>

      {isOpen && (
        <div className="absolute right-0 mt-2 w-80 bg-white rounded-lg shadow-lg border border-gray-200 z-50 max-h-96 overflow-y-auto">
          <div className="p-4 border-b border-gray-200">
            <div className="flex items-center justify-between">
              <h3 className="font-semibold text-gray-900">Notifications</h3>
              <Button
                variant="ghost"
                size="sm"
                onClick={() => setIsOpen(false)}
              >
                <X className="h-4 w-4" />
              </Button>
            </div>
          </div>

          <div className="max-h-64 overflow-y-auto">
            {notifications.length === 0 ? (
              <div className="p-4 text-center text-gray-500">
                No notifications
              </div>
            ) : (
              notifications.map((notification) => (
                <div
                  key={notification.id}
                  className={cn(
                    "p-4 border-l-4 border-b border-gray-100 hover:bg-gray-50 transition-colors",
                    getNotificationColor(notification.type),
                    !notification.read && "bg-opacity-100"
                  )}
                >
                  <div className="flex items-start space-x-3">
                    {getNotificationIcon(notification.type)}
                    <div className="flex-1 min-w-0">
                      <p className="text-sm font-medium text-gray-900">
                        {notification.title}
                      </p>
                      <p className="text-sm text-gray-600 mt-1">
                        {notification.message}
                      </p>
                      <p className="text-xs text-gray-400 mt-2">
                        {notification.timestamp}
                      </p>
                      {notification.actionLabel && (
                        <Button
                          variant="outline"
                          size="sm"
                          className="mt-2"
                          onClick={() => onMarkAsRead(notification.id)}
                        >
                          {notification.actionLabel}
                        </Button>
                      )}
                    </div>
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => onDismiss(notification.id)}
                    >
                      <X className="h-3 w-3" />
                    </Button>
                  </div>
                </div>
              ))
            )}
          </div>

          {notifications.length > 0 && (
            <div className="p-4 border-t border-gray-200">
              <Button
                variant="outline"
                size="sm"
                className="w-full"
                onClick={() => notifications.forEach(n => onMarkAsRead(n.id))}
              >
                Mark All as Read
              </Button>
            </div>
          )}
        </div>
      )}
    </div>
  );
}

interface MetricCardProps {
  title: string;
  value: string | number;
  change?: number;
  changeLabel?: string;
  icon: React.ElementType;
  color: 'blue' | 'green' | 'yellow' | 'red' | 'purple' | 'indigo';
  loading?: boolean;
  className?: string;
}

export function SmartMetricCard({
  title,
  value,
  change,
  changeLabel,
  icon: Icon,
  color,
  loading = false,
  className
}: MetricCardProps) {
  const colorClasses = {
    blue: 'border-l-blue-500 bg-blue-50 text-blue-600',
    green: 'border-l-green-500 bg-green-50 text-green-600',
    yellow: 'border-l-yellow-500 bg-yellow-50 text-yellow-600',
    red: 'border-l-red-500 bg-red-50 text-red-600',
    purple: 'border-l-purple-500 bg-purple-50 text-purple-600',
    indigo: 'border-l-indigo-500 bg-indigo-50 text-indigo-600'
  };

  const iconColorClasses = {
    blue: 'bg-blue-100 text-blue-600',
    green: 'bg-green-100 text-green-600',
    yellow: 'bg-yellow-100 text-yellow-600',
    red: 'bg-red-100 text-red-600',
    purple: 'bg-purple-100 text-purple-600',
    indigo: 'bg-indigo-100 text-indigo-600'
  };

  if (loading) {
    return (
      <Card className={cn("p-6 animate-pulse", className)}>
        <div className="flex items-center justify-between">
          <div className="space-y-2">
            <div className="h-4 bg-gray-200 rounded w-20"></div>
            <div className="h-8 bg-gray-200 rounded w-16"></div>
            <div className="h-3 bg-gray-200 rounded w-12"></div>
          </div>
          <div className="h-12 w-12 bg-gray-200 rounded-lg"></div>
        </div>
      </Card>
    );
  }

  return (
    <Card className={cn(
      "p-6 hover:shadow-lg hover-lift transition-all duration-300 border-l-4",
      colorClasses[color],
      className
    )}>
      <div className="flex items-center justify-between">
        <div>
          <p className="text-sm font-medium text-gray-600">{title}</p>
          <p className="text-3xl font-bold text-gray-900 mt-1">{value}</p>
          {change !== undefined && (
            <p className={cn(
              "text-sm flex items-center mt-2",
              change >= 0 ? "text-green-600" : "text-red-600"
            )}>
              {change >= 0 ? (
                <TrendingUp className="h-3 w-3 mr-1" />
              ) : (
                <TrendingDown className="h-3 w-3 mr-1" />
              )}
              {Math.abs(change)}% {changeLabel || 'vs last period'}
            </p>
          )}
        </div>
        <div className={cn("p-3 rounded-lg", iconColorClasses[color])}>
          <Icon className="h-8 w-8" />
        </div>
      </div>
    </Card>
  );
}

interface QuickActionProps {
  title: string;
  description: string;
  icon: React.ElementType;
  onClick: () => void;
  variant?: 'primary' | 'secondary';
  className?: string;
}

export function QuickActionCard({
  title,
  description,
  icon: Icon,
  onClick,
  variant = 'secondary',
  className
}: QuickActionProps) {
  return (
    <Card 
      className={cn(
        "p-4 hover:shadow-lg hover-lift transition-all duration-300 cursor-pointer group",
        variant === 'primary' && "border-blue-200 bg-blue-50",
        className
      )}
      onClick={onClick}
    >
      <div className="flex items-center space-x-3">
        <div className={cn(
          "p-2 rounded-lg",
          variant === 'primary' ? "bg-blue-100" : "bg-gray-100"
        )}>
          <Icon className={cn(
            "h-5 w-5",
            variant === 'primary' ? "text-blue-600" : "text-gray-600"
          )} />
        </div>
        <div className="flex-1">
          <h4 className="font-medium text-gray-900 group-hover:text-blue-600 transition-colors">
            {title}
          </h4>
          <p className="text-sm text-gray-600">{description}</p>
        </div>
      </div>
    </Card>
  );
}
