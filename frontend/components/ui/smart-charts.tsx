'use client';

import { useState } from 'react';
import { Card } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { cn } from '@/lib/utils';
import {
  TrendingUp,
  TrendingDown,
  MoreHorizontal,
  Download,
  RefreshCw,
  Calendar,
  Filter
} from 'lucide-react';

interface ChartData {
  label: string;
  value: number;
  color?: string;
  trend?: number;
}

interface SmartChartProps {
  title: string;
  subtitle?: string;
  data: ChartData[];
  type: 'bar' | 'line' | 'pie' | 'area';
  height?: number;
  loading?: boolean;
  showTrend?: boolean;
  showLegend?: boolean;
  className?: string;
  onRefresh?: () => void;
  onExport?: () => void;
}

export function SmartChart({
  title,
  subtitle,
  data,
  type,
  height = 300,
  loading = false,
  showTrend = true,
  showLegend = true,
  className,
  onRefresh,
  onExport
}: SmartChartProps) {
  const [timeRange, setTimeRange] = useState('7d');

  const maxValue = Math.max(...data.map(d => d.value));
  const totalValue = data.reduce((sum, d) => sum + d.value, 0);

  const getBarHeight = (value: number) => {
    return (value / maxValue) * 100;
  };

  const renderBarChart = () => (
    <div className="flex items-end justify-between h-full space-x-2 px-4">
      {data.map((item, index) => (
        <div key={index} className="flex flex-col items-center flex-1">
          <div
            className={`w-full rounded-t-md transition-all duration-500 hover:opacity-80 ${
              item.color || 'bg-blue-500'
            }`}
            style={{ height: `${getBarHeight(item.value)}%` }}
            title={`${item.label}: ${item.value}`}
          />
          <span className="text-xs text-gray-600 mt-2 text-center truncate w-full">
            {item.label}
          </span>
        </div>
      ))}
    </div>
  );

  const renderLineChart = () => {
    const points = data.map((item, index) => {
      const x = (index / (data.length - 1)) * 100;
      const y = 100 - getBarHeight(item.value);
      return `${x},${y}`;
    }).join(' ');

    return (
      <div className="relative h-full px-4">
        <svg className="w-full h-full" viewBox="0 0 100 100" preserveAspectRatio="none">
          <polyline
            fill="none"
            stroke="rgb(59, 130, 246)"
            strokeWidth="2"
            points={points}
            className="transition-all duration-500"
          />
          {data.map((item, index) => {
            const x = (index / (data.length - 1)) * 100;
            const y = 100 - getBarHeight(item.value);
            return (
              <circle
                key={index}
                cx={x}
                cy={y}
                r="2"
                fill="rgb(59, 130, 246)"
                className="hover:r-3 transition-all duration-200"
              />
            );
          })}
        </svg>
        <div className="absolute bottom-0 left-4 right-4 flex justify-between">
          {data.map((item, index) => (
            <span key={index} className="text-xs text-gray-600">
              {item.label}
            </span>
          ))}
        </div>
      </div>
    );
  };

  const renderPieChart = () => {
    let cumulativePercentage = 0;
    const radius = 45;
    const centerX = 50;
    const centerY = 50;

    return (
      <div className="flex items-center justify-center h-full">
        <div className="relative">
          <svg width="200" height="200" viewBox="0 0 100 100" className="transform -rotate-90">
            {data.map((item, index) => {
              const percentage = (item.value / totalValue) * 100;
              const strokeDasharray = `${percentage} ${100 - percentage}`;
              const strokeDashoffset = -cumulativePercentage;
              cumulativePercentage += percentage;

              return (
                <circle
                  key={index}
                  cx={centerX}
                  cy={centerY}
                  r={radius}
                  fill="transparent"
                  stroke={item.color || `hsl(${index * 60}, 70%, 50%)`}
                  strokeWidth="10"
                  strokeDasharray={strokeDasharray}
                  strokeDashoffset={strokeDashoffset}
                  className="transition-all duration-500 hover:stroke-width-12"
                  style={{
                    transformOrigin: `${centerX}% ${centerY}%`
                  }}
                />
              );
            })}
          </svg>
          <div className="absolute inset-0 flex items-center justify-center">
            <div className="text-center">
              <div className="text-2xl font-bold text-gray-900">{totalValue}</div>
              <div className="text-xs text-gray-600">Total</div>
            </div>
          </div>
        </div>
      </div>
    );
  };

  const renderAreaChart = () => {
    const points = data.map((item, index) => {
      const x = (index / (data.length - 1)) * 100;
      const y = 100 - getBarHeight(item.value);
      return `${x},${y}`;
    }).join(' ');

    const areaPoints = `0,100 ${points} 100,100`;

    return (
      <div className="relative h-full px-4">
        <svg className="w-full h-full" viewBox="0 0 100 100" preserveAspectRatio="none">
          <defs>
            <linearGradient id="areaGradient" x1="0%" y1="0%" x2="0%" y2="100%">
              <stop offset="0%" stopColor="rgb(59, 130, 246)" stopOpacity="0.3" />
              <stop offset="100%" stopColor="rgb(59, 130, 246)" stopOpacity="0.1" />
            </linearGradient>
          </defs>
          <polygon
            fill="url(#areaGradient)"
            points={areaPoints}
            className="transition-all duration-500"
          />
          <polyline
            fill="none"
            stroke="rgb(59, 130, 246)"
            strokeWidth="2"
            points={points}
            className="transition-all duration-500"
          />
        </svg>
        <div className="absolute bottom-0 left-4 right-4 flex justify-between">
          {data.map((item, index) => (
            <span key={index} className="text-xs text-gray-600">
              {item.label}
            </span>
          ))}
        </div>
      </div>
    );
  };

  const renderChart = () => {
    switch (type) {
      case 'bar':
        return renderBarChart();
      case 'line':
        return renderLineChart();
      case 'pie':
        return renderPieChart();
      case 'area':
        return renderAreaChart();
      default:
        return renderBarChart();
    }
  };

  if (loading) {
    return (
      <Card className={cn("p-6", className)}>
        <div className="animate-pulse">
          <div className="h-4 bg-gray-200 rounded w-1/3 mb-2"></div>
          <div className="h-3 bg-gray-200 rounded w-1/2 mb-4"></div>
          <div className="h-64 bg-gray-200 rounded"></div>
        </div>
      </Card>
    );
  }

  return (
    <Card className={cn("overflow-hidden", className)}>
      {/* Chart Header */}
      <div className="p-6 border-b border-gray-200">
        <div className="flex items-center justify-between">
          <div>
            <h3 className="text-lg font-semibold text-gray-900">{title}</h3>
            {subtitle && (
              <p className="text-sm text-gray-600 mt-1">{subtitle}</p>
            )}
          </div>
          <div className="flex items-center space-x-2">
            <select
              value={timeRange}
              onChange={(e) => setTimeRange(e.target.value)}
              className="text-sm border border-gray-300 rounded-md px-3 py-1 focus:outline-none focus:ring-2 focus:ring-blue-500"
            >
              <option value="7d">Last 7 days</option>
              <option value="30d">Last 30 days</option>
              <option value="90d">Last 90 days</option>
              <option value="1y">Last year</option>
            </select>
            {onRefresh && (
              <Button variant="ghost" size="sm" onClick={onRefresh}>
                <RefreshCw className="h-4 w-4" />
              </Button>
            )}
            {onExport && (
              <Button variant="ghost" size="sm" onClick={onExport}>
                <Download className="h-4 w-4" />
              </Button>
            )}
            <Button variant="ghost" size="sm">
              <MoreHorizontal className="h-4 w-4" />
            </Button>
          </div>
        </div>
      </div>

      {/* Chart Content */}
      <div className="p-6">
        <div style={{ height: `${height}px` }}>
          {renderChart()}
        </div>
      </div>

      {/* Legend */}
      {showLegend && type !== 'pie' && (
        <div className="px-6 pb-6">
          <div className="flex flex-wrap gap-4">
            {data.map((item, index) => (
              <div key={index} className="flex items-center space-x-2">
                <div
                  className={`w-3 h-3 rounded-full ${
                    item.color || 'bg-blue-500'
                  }`}
                />
                <span className="text-sm text-gray-600">{item.label}</span>
                <span className="text-sm font-medium text-gray-900">
                  {item.value}
                </span>
                {showTrend && item.trend !== undefined && (
                  <span className={`text-xs flex items-center ${
                    item.trend >= 0 ? 'text-green-600' : 'text-red-600'
                  }`}>
                    {item.trend >= 0 ? (
                      <TrendingUp className="h-3 w-3 mr-1" />
                    ) : (
                      <TrendingDown className="h-3 w-3 mr-1" />
                    )}
                    {Math.abs(item.trend)}%
                  </span>
                )}
              </div>
            ))}
          </div>
        </div>
      )}

      {/* Pie Chart Legend */}
      {showLegend && type === 'pie' && (
        <div className="px-6 pb-6">
          <div className="grid grid-cols-2 gap-2">
            {data.map((item, index) => (
              <div key={index} className="flex items-center space-x-2">
                <div
                  className="w-3 h-3 rounded-full"
                  style={{
                    backgroundColor: item.color || `hsl(${index * 60}, 70%, 50%)`
                  }}
                />
                <span className="text-sm text-gray-600 truncate">{item.label}</span>
                <span className="text-sm font-medium text-gray-900">
                  {((item.value / totalValue) * 100).toFixed(1)}%
                </span>
              </div>
            ))}
          </div>
        </div>
      )}
    </Card>
  );
}
